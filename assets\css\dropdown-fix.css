/* Enhanced dropdown menu behavior for consistent accessibility across all pages */

/* Define fadeIn animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Override any conflicting styles from style.css */
.user-menu {
  position: relative;
}

/* Override the default dropdown styles to ensure consistency */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 220px; /* Increased width for easier clicking */
  z-index: 1000;
  margin-top: 10px;
  display: none;
  overflow: hidden;
  padding: 5px 0; /* Consistent padding */

  /* Add a larger invisible area to prevent the dropdown from closing too quickly */
  padding-top: 15px;
  padding-bottom: 5px;
}

/* Create a larger invisible area above the dropdown to make it easier to move from button to dropdown */
.user-dropdown::before {
  content: "";
  position: absolute;
  top: -25px; /* Increased for better hover area */
  left: -25px;
  right: -25px;
  height: 25px;
  background-color: transparent;
}

/* Add a visual indicator for the dropdown */
.user-dropdown::after {
  content: "";
  position: absolute;
  top: 0;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  transform: translateY(-100%);
}

/* Remove any hover-based display from style.css */
.user-menu:hover .user-dropdown {
  /* This will be overridden by JavaScript */
  /* We're not removing this completely to maintain fallback behavior */
  display: block;
  animation: fadeIn 0.3s;
}

/* Keep dropdown visible when hovering over it */
.user-dropdown:hover {
  display: block;
}

/* Add a transition delay when leaving the dropdown */
.user-menu .user-dropdown {
  transition: visibility 0.3s, opacity 0.3s;
  visibility: hidden;
  opacity: 0;
  pointer-events: none; /* Prevent interaction when hidden */
}

/* JavaScript-controlled visibility - this class is added by our dropdown.js */
.user-dropdown.show {
  display: block !important; /* Use !important to override any conflicting styles */
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important; /* Allow interaction when visible */
  animation: fadeIn 0.3s;
}

/* Improve dropdown item styling */
.user-dropdown a {
  display: block;
  padding: 15px 20px; /* Increased padding for larger click area */
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s, color 0.2s;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px; /* Ensure readable font size */
  line-height: 1.5; /* Improved line height for readability */
  white-space: nowrap; /* Prevent text wrapping */
}

.user-dropdown a:last-child {
  border-bottom: none;
}

.user-dropdown a:hover {
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
}

.user-dropdown a:focus {
  outline: 2px solid #b2d8b2; /* Accessibility focus outline */
  outline-offset: -2px;
  background-color: #f0f8e8;
}

.user-dropdown a.active {
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  font-weight: bold;
}

.user-dropdown i {
  margin-right: 10px; /* Increased spacing */
  width: 18px; /* Slightly larger icons */
  text-align: center;
  font-size: 18px; /* Larger icons */
}

/* Add styles for JavaScript-enabled browsers */
.js-enabled .user-dropdown {
  /* These styles will only apply when JavaScript is enabled */
  display: none;
  visibility: hidden;
  opacity: 0;
}

/* Ensure the dropdown is visible on touch devices */
@media (hover: none) {
  .user-dropdown.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

