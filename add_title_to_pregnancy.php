<?php
// Include database connection
require_once 'includes/database.php';

// Check if title column exists in Pregnancy table
$checkColumnSql = "SHOW COLUMNS FROM Pregnancy LIKE 'title'";
$columnResult = $connection->query($checkColumnSql);

if (!$columnResult || $columnResult->num_rows === 0) {
    // Title column doesn't exist, add it
    echo "<p>Title column doesn't exist in Pregnancy table. Adding it...</p>";
    
    $alterTableSql = "ALTER TABLE Pregnancy ADD COLUMN title VARCHAR(255) AFTER user_id";
    
    if ($connection->query($alterTableSql)) {
        echo "<p style='color: green;'>Title column added successfully to Pregnancy table.</p>";
        
        // Update existing pregnancies with a default title
        $updateSql = "UPDATE Pregnancy SET title = CONCAT('Pregnancy due ', due_date) WHERE title IS NULL";
        
        if ($connection->query($updateSql)) {
            echo "<p style='color: green;'>Updated existing pregnancies with default titles.</p>";
        } else {
            echo "<p style='color: red;'>Error updating existing pregnancies: " . $connection->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error adding title column: " . $connection->error . "</p>";
    }
} else {
    echo "<p>Title column already exists in Pregnancy table.</p>";
}

// Display current pregnancies
$sql = "SELECT * FROM Pregnancy";
$result = $connection->query($sql);

if (!$result) {
    echo "<p>Error querying Pregnancy table: " . $connection->error . "</p>";
} else if ($result->num_rows === 0) {
    echo "<p>No pregnancies found in the database.</p>";
    
    // Add a sample pregnancy for testing
    echo "<p>Adding a sample pregnancy for testing...</p>";
    
    $userId = 1; // Assuming user ID 1 exists
    $startDate = date('Y-m-d');
    $dueDate = date('Y-m-d', strtotime('+9 months'));
    $title = "Sample Pregnancy";
    $notes = "This is a sample pregnancy for testing.";
    
    $insertSql = "INSERT INTO Pregnancy (user_id, title, start_date, due_date, notes) VALUES (?, ?, ?, ?, ?)";
    $stmt = $connection->prepare($insertSql);
    $stmt->bind_param("issss", $userId, $title, $startDate, $dueDate, $notes);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>Sample pregnancy added successfully.</p>";
    } else {
        echo "<p style='color: red;'>Error adding sample pregnancy: " . $stmt->error . "</p>";
    }
    
    // Refresh the result
    $result = $connection->query($sql);
}

if ($result && $result->num_rows > 0) {
    echo "<h2>Current Pregnancies</h2>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>User ID</th><th>Title</th><th>Start Date</th><th>Due Date</th><th>Notes</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['user_id'] . "</td>";
        echo "<td>" . (isset($row['title']) ? $row['title'] : 'N/A') . "</td>";
        echo "<td>" . $row['start_date'] . "</td>";
        echo "<td>" . $row['due_date'] . "</td>";
        echo "<td>" . $row['notes'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}
?>
