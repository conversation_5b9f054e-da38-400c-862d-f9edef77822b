<?php
// Include authentication check
require_once __DIR__ . '/../includes/auth_check.php';

// Set page title and active page
$pageTitle = 'Agenda';
$activePage = 'agenda';

// Additional head content for the page
$additionalHeadContent = '<script src="' . $rootPath . 'assets/js/agenda.js"></script>';

// Include header
include __DIR__ . '/../includes/header.php';

// Include required functions
require_once __DIR__ . '/../includes/agenda_functions.php';
require_once __DIR__ . '/../includes/appointment_functions.php';
require_once __DIR__ . '/../includes/health_functions.php';

// Check if user is logged in
$isLoggedIn = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
$userId = $isLoggedIn ? $_SESSION['user_id'] : 0;

// Handle form submissions
$message = '';
$messageType = '';

if ($isLoggedIn && isset($_GET['action'])) {
    $action = $_GET['action'];

    // Delete agenda item
    if ($action === 'delete' && isset($_GET['id'])) {
        $itemId = intval($_GET['id']);
        if (deleteAgendaItem($itemId, $userId)) {
            $message = "Item deleted successfully.";
            $messageType = "success";
        } else {
            $message = "Error deleting item.";
            $messageType = "error";
        }
    }

    // Add new agenda item
    if ($action === 'add' && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $title = isset($_POST['title']) ? trim($_POST['title']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $date = isset($_POST['date']) ? trim($_POST['date']) : '';
        $time = isset($_POST['time']) ? trim($_POST['time']) : '';
        $babyId = isset($_POST['baby_id']) && !empty($_POST['baby_id']) ? intval($_POST['baby_id']) : null;

        if (!empty($title) && !empty($date)) {
            if (addAgendaItem($userId, $title, $description, $date, $time, $babyId)) {
                $message = "Item added successfully.";
                $messageType = "success";
            } else {
                $message = "Error adding item.";
                $messageType = "error";
            }
        } else {
            $message = "Please fill in all required fields.";
            $messageType = "error";
        }
    }

    // Update agenda item
    if ($action === 'update' && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $itemId = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $title = isset($_POST['title']) ? trim($_POST['title']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $date = isset($_POST['date']) ? trim($_POST['date']) : '';
        $time = isset($_POST['time']) ? trim($_POST['time']) : '';
        $babyId = isset($_POST['baby_id']) && !empty($_POST['baby_id']) ? intval($_POST['baby_id']) : null;

        if (!empty($title) && !empty($date) && $itemId > 0) {
            if (updateAgendaItem($itemId, $userId, $title, $description, $date, $time, $babyId)) {
                $message = "Item updated successfully.";
                $messageType = "success";
            } else {
                $message = "Error updating item.";
                $messageType = "error";
            }
        } else {
            $message = "Please fill in all required fields.";
            $messageType = "error";
        }
    }
}

// Calendar setup
$currentMonth = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
$currentYear = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));

// Calculate previous and next month/year
$prevMonth = $currentMonth - 1;
$prevYear = $currentYear;
if ($prevMonth < 1) {
    $prevMonth = 12;
    $prevYear--;
}

$nextMonth = $currentMonth + 1;
$nextYear = $currentYear;
if ($nextMonth > 12) {
    $nextMonth = 1;
    $nextYear++;
}

// Get month name
$monthName = date('F', mktime(0, 0, 0, $currentMonth, 1, $currentYear));

// Get calendar events if user is logged in
$agendaItems = [];
if ($isLoggedIn) {
    $agendaItems = getAgendaItems($userId);
}

// Check if a specific date is selected
$selectedDate = isset($_GET['date']) ? $_GET['date'] : null;

// Helper function for calendar day classes
function getCalendarDayClass($day, $month, $year, $currentMonth, $selectedDate = null)
{
    $class = 'calendar-day';

    // Add 'other-month' class for days not in current month
    if ($month != $currentMonth) {
        $class .= ' other-month';
    }

    // Format the date for comparison
    $checkDate = sprintf('%04d-%02d-%02d', $year, $month, $day);

    // Add 'today' class for current day
    $today = date('Y-m-d');
    if ($checkDate == $today) {
        $class .= ' today';
    }

    // Add 'selected' class for selected date
    if ($selectedDate && $checkDate == $selectedDate) {
        $class .= ' selected';
    }

    return $class;
}

// Helper function to get events for a specific day
function getEventsForDay($agendaItems, $year, $month, $day)
{
    $date = sprintf('%04d-%02d-%02d', $year, $month, $day);
    $events = [];

    foreach ($agendaItems as $item) {
        if ($item['date'] === $date) {
            $events[] = $item;
        }
    }

    return $events;
}

// Helper function to get all unique dates with events
function getUniqueDatesWithEvents($agendaItems)
{
    $dates = [];

    if (!empty($agendaItems)) {
        foreach ($agendaItems as $item) {
            if (isset($item['date']) && !empty($item['date'])) {
                $date = $item['date'];
                if (!in_array($date, $dates)) {
                    $dates[] = $date;
                }
            }
        }

        // Sort dates
        sort($dates);
    }

    return $dates;
}

// Get the first day of the month
$firstDayOfMonth = mktime(0, 0, 0, $currentMonth, 1, $currentYear);
$numberDays = date('t', $firstDayOfMonth);
$dateComponents = getdate($firstDayOfMonth);
$monthName = $dateComponents['month'];
$dayOfWeek = $dateComponents['wday'];

// Create calendar array
$calendar = [];
$calendar[0] = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

// Create the rest of the calendar
$currentDay = 1;
$calendar[1] = array_fill(0, 7, '');

// Fill the first week of the calendar
for ($i = $dayOfWeek; $i < 7; $i++) {
    $calendar[1][$i] = $currentDay;
    $currentDay++;
}

// Fill the remaining weeks
$currentWeek = 2;
while ($currentDay <= $numberDays) {
    $calendar[$currentWeek] = array_fill(0, 7, '');

    for ($i = 0; $i < 7 && $currentDay <= $numberDays; $i++) {
        $calendar[$currentWeek][$i] = $currentDay;
        $currentDay++;
    }

    $currentWeek++;
}


?>

<main>
    <?php if ($isLoggedIn): ?>
        <section class="agenda-container">
            <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <div class="section-header">
                <h2>Your Agenda</h2>
                <div class="section-controls">
                    <div class="view-toggle">
                        <button id="calendar-view" class="active">Calendar View</button>
                        <button id="list-view">List View</button>
                        <button id="add-new-item" class="add-btn">+ Add New Item</button>
                    </div>
                </div>
            </div>

            <div class="agenda-content">
                <!-- Calendar View -->
                <div id="calendar-view-container" class="calendar-container">
                    <div class="calendar-header">
                        <div class="calendar-navigation">
                            <a href="?month=<?php echo $prevMonth; ?>&year=<?php echo $prevYear; ?>" id="prev-month" class="nav-arrow"><i class="fas fa-chevron-left"></i></a>
                            <div class="current-month">
                                <?php echo $monthName . ' ' . $currentYear; ?>
                            </div>
                            <a href="?month=<?php echo $nextMonth; ?>&year=<?php echo $nextYear; ?>" id="next-month" class="nav-arrow"><i class="fas fa-chevron-right"></i></a>
                        </div>
                        <a href="?month=<?php echo date('m'); ?>&year=<?php echo date('Y'); ?>" id="today-btn" class="today-btn">Today</a>
                    </div>

                    <div class="calendar">
                        <div class="calendar-grid">
                            <?php foreach ($calendar[0] as $dayName): ?>
                                <div class="calendar-day-header"><?php echo $dayName; ?></div>
                            <?php endforeach; ?>
                        </div>

                        <?php for ($week = 1; $week < count($calendar); $week++): ?>
                            <div class="calendar-week">
                                <?php for ($day = 0; $day < 7; $day++): ?>
                                    <?php
                                    $currentDay = $calendar[$week][$day];
                                    if ($currentDay === '') {
                                        echo '<div class="calendar-day empty"></div>';
                                        continue;
                                    }

                                    // Determine if this day is in the previous or next month
                                    $month = $currentMonth;
                                    $year = $currentYear;

                                    // Get the class for this day
                                    $dayClass = getCalendarDayClass($currentDay, $month, $year, $currentMonth, $selectedDate);

                                    // Get events for this day
                                    $dayEvents = getEventsForDay($agendaItems, $year, $month, $currentDay);
                                    ?>
                                    <div class="<?php echo $dayClass; ?>">
                                        <div class="day-number"><?php echo $currentDay; ?></div>
                                        <div class="day-events">
                                            <?php foreach ($dayEvents as $event): ?>
                                                <?php
                                                $eventClass = 'event manual';
                                                if (strpos($event['title'], 'Appointment') !== false) {
                                                    $eventClass = 'event appointment';
                                                } elseif (strpos($event['title'], 'Symptom') !== false) {
                                                    $eventClass = 'event symptom';
                                                }
                                                ?>
                                                <div class="<?php echo $eventClass; ?>" data-id="<?php echo $event['id']; ?>">
                                                    <?php echo htmlspecialchars($event['title']); ?>
                                                    <div class="event-tooltip">
                                                        <div class="event-time"><?php echo date('g:i A', strtotime($event['time'])); ?></div>
                                                        <div class="event-description"><?php echo htmlspecialchars($event['description']); ?></div>
                                                        <?php if (!empty($event['baby_name'])): ?>
                                                            <div class="event-baby">Baby: <?php echo htmlspecialchars($event['baby_name']); ?></div>
                                                        <?php endif; ?>
                                                        <div class="event-actions">
                                                            <button class="edit-btn" data-id="<?php echo $event['id']; ?>"><i class="fas fa-edit"></i></button>
                                                            <button class="delete-btn" data-id="<?php echo $event['id']; ?>"><i class="fas fa-trash"></i></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>

                <!-- List View -->
                <div id="list-view-container" class="list-container" style="display: none;">
                    <?php if (empty($agendaItems)): ?>
                        <div class="empty-agenda">
                            <i class="far fa-calendar-alt"></i>
                            <p>You don't have any agenda items yet.</p>
                            <p>Click the "Add New Item" button to get started.</p>
                        </div>
                    <?php else: ?>
                        <?php
                        // Group items by month
                        $groupedItems = [];
                        $monthNames = [
                            1 => 'January',
                            2 => 'February',
                            3 => 'March',
                            4 => 'April',
                            5 => 'May',
                            6 => 'June',
                            7 => 'July',
                            8 => 'August',
                            9 => 'September',
                            10 => 'October',
                            11 => 'November',
                            12 => 'December'
                        ];

                        foreach ($agendaItems as $item) {
                            $monthNum = date('n', strtotime($item['date']));
                            $year = date('Y', strtotime($item['date']));
                            $month = $monthNames[$monthNum] . ' ' . $year;

                            if (!isset($groupedItems[$month])) {
                                $groupedItems[$month] = [];
                            }

                            $groupedItems[$month][] = $item;
                        }

                        // Sort by date
                        foreach ($groupedItems as &$monthItems) {
                            usort($monthItems, function ($a, $b) {
                                $dateA = strtotime($a['date'] . ' ' . $a['time']);
                                $dateB = strtotime($b['date'] . ' ' . $b['time']);
                                return $dateA - $dateB;
                            });
                        }
                        ?>

                        <div class="agenda-list">
                            <?php foreach ($groupedItems as $month => $monthItems): ?>
                                <h3 class="month-header"><?php echo $month; ?></h3>

                                <?php foreach ($monthItems as $item): ?>
                                    <div class="agenda-item">
                                        <div class="agenda-date">
                                            <div class="day"><?php echo date('j', strtotime($item['date'])); ?></div>
                                            <div class="month"><?php echo date('M', strtotime($item['date'])); ?></div>
                                        </div>
                                        <div class="agenda-time">
                                            <?php echo date('g:i A', strtotime($item['time'])); ?>
                                        </div>
                                        <div class="agenda-details">
                                            <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                                            <p><?php echo htmlspecialchars($item['description']); ?></p>
                                            <?php if (!empty($item['baby_name'])): ?>
                                                <div class="agenda-baby">Baby: <?php echo htmlspecialchars($item['baby_name']); ?></div>
                                            <?php endif; ?>
                                            <?php
                                            $sourceClass = 'source-manual';
                                            if (strpos($item['title'], 'Appointment') !== false) {
                                                $sourceClass = 'source-appointment';
                                            } elseif (strpos($item['title'], 'Symptom') !== false) {
                                                $sourceClass = 'source-symptom';
                                            }
                                            ?>
                                            <span class="agenda-source <?php echo $sourceClass; ?>">
                                                <?php
                                                if (strpos($item['title'], 'Appointment') !== false) {
                                                    echo 'Appointment';
                                                } elseif (strpos($item['title'], 'Symptom') !== false) {
                                                    echo 'Symptom';
                                                } else {
                                                    echo 'Manual Entry';
                                                }
                                                ?>
                                            </span>
                                        </div>
                                        <div class="agenda-actions">
                                            <button class="edit-btn" data-id="<?php echo $item['id']; ?>" title="Edit"><i class="fas fa-edit"></i></button>
                                            <button class="delete-btn" data-id="<?php echo $item['id']; ?>" title="Delete"><i class="fas fa-trash"></i></button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Add Item Modal -->
            <div id="add-item-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>Add New Item to Agenda</h2>
                    <div class="category-selection">
                        <div class="category-option">
                            <a href="<?php echo $rootPath; ?>pages/health.php" class="category-link">
                                <i class="fas fa-heartbeat"></i>
                                <h3>Health Symptom</h3>
                                <p>Record a new health symptom or condition</p>
                            </a>
                        </div>
                        <div class="category-option">
                            <a href="<?php echo $rootPath; ?>pages/appointments.php" class="category-link">
                                <i class="fas fa-calendar-check"></i>
                                <h3>Appointment</h3>
                                <p>Schedule a new medical appointment</p>
                            </a>
                        </div>
                        <div class="category-option">
                            <a href="#" class="category-link manual-entry">
                                <i class="fas fa-pencil-alt"></i>
                                <h3>Manual Entry</h3>
                                <p>Add a custom agenda item</p>
                            </a>
                        </div>
                    </div>
                    
                    <!-- This div will be populated with the form when Manual Entry is clicked -->
                    <div id="manual-entry-container" style="display: none;"></div>
                </div>
            </div>

            <!-- Hidden form template for manual entries -->
            <div id="manual-entry-form" style="display: none;">
                <form action="?action=add" method="post">
                    <div class="form-group">
                        <label for="title">Title:</label>
                        <input type="text" id="title" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description:</label>
                        <textarea id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="date">Date:</label>
                        <input type="date" id="date" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="time">Time:</label>
                        <input type="time" id="time" name="time">
                    </div>
                    <button type="submit" class="btn">Add Item</button>
                </form>
            </div>

            <!-- Edit Item Modal -->
            <div id="edit-item-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>Edit Agenda Item</h2>
                    <form action="?action=update" method="post">
                        <input type="hidden" id="edit-item-id" name="id">
                        <div class="form-group">
                            <label for="edit-item-title">Title:</label>
                            <input type="text" id="edit-item-title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-item-description">Description:</label>
                            <textarea id="edit-item-description" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-item-date">Date:</label>
                            <input type="date" id="edit-item-date" name="date" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-item-time">Time:</label>
                            <input type="time" id="edit-item-time" name="time">
                        </div>
                        <button type="submit" class="btn">Update Item</button>
                    </form>
                </div>
            </div>
        </section>
    <?php else: ?>
        <div class="agenda-container">
            <div class="login-prompt">
                <h2>Access Your Agenda</h2>
                <p>Please log in to view and manage your agenda items.</p>
                <a href="#" class="login-trigger login-prompt-btn">Login</a>
            </div>
        </div>
    <?php endif; ?>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>
