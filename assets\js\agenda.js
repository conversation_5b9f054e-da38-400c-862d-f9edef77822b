/**
 * Agenda Page Functionality
 * Handles calendar and list views, and agenda item management
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize agenda functionality
    // View toggle functionality
    const calendarViewBtn = document.getElementById('calendar-view');
    const listViewBtn = document.getElementById('list-view');
    const calendarView = document.getElementById('calendar-view-container');
    const listView = document.getElementById('list-view-container');

    if (calendarViewBtn && listViewBtn && calendarView && listView) {
        calendarViewBtn.addEventListener('click', function() {
            calendarView.style.display = 'block';
            listView.style.display = 'none';
            calendarViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');

            // Store the user's preference
            localStorage.setItem('agendaViewPreference', 'calendar');
        });

        listViewBtn.addEventListener('click', function() {
            calendarView.style.display = 'none';
            listView.style.display = 'block';
            listViewBtn.classList.add('active');
            calendarViewBtn.classList.remove('active');

            // Store the user's preference
            localStorage.setItem('agendaViewPreference', 'list');
        });

        // Check for stored preferences
        const viewPreference = localStorage.getItem('agendaViewPreference');
        if (viewPreference === 'list') {
            listViewBtn.click();
        } else {
            calendarViewBtn.click();
        }
    }

    // Calendar navigation
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const todayBtn = document.getElementById('today-btn');

    if (prevMonthBtn && nextMonthBtn && todayBtn) {
        prevMonthBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const url = new URL(window.location.href);
            const currentMonth = parseInt(url.searchParams.get('month')) || new Date().getMonth() + 1;
            const currentYear = parseInt(url.searchParams.get('year')) || new Date().getFullYear();

            let newMonth = currentMonth - 1;
            let newYear = currentYear;

            if (newMonth < 1) {
                newMonth = 12;
                newYear--;
            }

            url.searchParams.set('month', newMonth);
            url.searchParams.set('year', newYear);

            // Preserve date list state
            const dateListVisible = localStorage.getItem('dateListVisible');
            if (dateListVisible === 'true') {
                url.searchParams.set('datelist', 'true');
            }

            window.location.href = url.toString();
        });

        nextMonthBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const url = new URL(window.location.href);
            const currentMonth = parseInt(url.searchParams.get('month')) || new Date().getMonth() + 1;
            const currentYear = parseInt(url.searchParams.get('year')) || new Date().getFullYear();

            let newMonth = currentMonth + 1;
            let newYear = currentYear;

            if (newMonth > 12) {
                newMonth = 1;
                newYear++;
            }

            url.searchParams.set('month', newMonth);
            url.searchParams.set('year', newYear);

            window.location.href = url.toString();
        });

        todayBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const today = new Date();
            const url = new URL(window.location.href);

            url.searchParams.set('month', today.getMonth() + 1);
            url.searchParams.set('year', today.getFullYear());

            window.location.href = url.toString();
        });
    }

    // Add new agenda item functionality
    const addNewItemBtn = document.getElementById('add-new-item');
    const addItemModal = document.getElementById('add-item-modal');
    const closeModalBtn = document.querySelector('#add-item-modal .close');
    const manualEntryLink = document.querySelector('.manual-entry');
    const manualEntryForm = document.getElementById('manual-entry-form');
    const manualEntryContainer = document.getElementById('manual-entry-container');

    if (addNewItemBtn && addItemModal && closeModalBtn) {
        addNewItemBtn.addEventListener('click', function(e) {
            e.preventDefault();
            addItemModal.style.display = 'block';
        });
        
        closeModalBtn.addEventListener('click', function() {
            addItemModal.style.display = 'none';
            // Reset if manual entry form was shown
            if (manualEntryContainer) {
                document.querySelector('.category-selection').style.display = 'block';
                manualEntryContainer.style.display = 'none';
                manualEntryContainer.innerHTML = '';
            }
        });

        // Manual entry option
        if (manualEntryLink && manualEntryForm && manualEntryContainer) {
            manualEntryLink.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelector('.category-selection').style.display = 'none';
                
                // Clone the form and append it to the container
                manualEntryContainer.innerHTML = manualEntryForm.innerHTML;
                manualEntryContainer.style.display = 'block';
            });
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === addItemModal) {
                addItemModal.style.display = 'none';
                // Reset if manual entry form was shown
                if (manualEntryContainer) {
                    document.querySelector('.category-selection').style.display = 'block';
                    manualEntryContainer.style.display = 'none';
                    manualEntryContainer.innerHTML = '';
                }
            }
        });
    }

    // Event tooltips
    const events = document.querySelectorAll('.event');
    events.forEach(event => {
        event.addEventListener('mouseenter', function() {
            const tooltip = this.querySelector('.event-tooltip');
            if (tooltip) {
                tooltip.style.display = 'block';
            }
        });

        event.addEventListener('mouseleave', function() {
            const tooltip = this.querySelector('.event-tooltip');
            if (tooltip) {
                tooltip.style.display = 'none';
            }
        });
    });

    // Delete agenda item functionality
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('Are you sure you want to delete this item?')) {
                const itemId = this.dataset.id;
                if (itemId) {
                    window.location.href = `?action=delete&id=${itemId}`;
                }
            }
        });
    });

    // Edit agenda item functionality
    const editButtons = document.querySelectorAll('.edit-btn');
    const editItemModal = document.getElementById('edit-item-modal');
    const closeEditModalBtn = document.querySelector('#edit-item-modal .close');

    if (editButtons.length > 0 && editItemModal && closeEditModalBtn) {
        editButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const itemId = this.dataset.id;
                if (itemId) {
                    // Fetch item details and populate the form
                    fetch(`?action=get_item&id=${itemId}`)
                        .then(response => response.json())
                        .then(data => {
                            document.getElementById('edit-item-id').value = data.id;
                            document.getElementById('edit-item-title').value = data.title;
                            document.getElementById('edit-item-description').value = data.description;
                            document.getElementById('edit-item-date').value = data.date;
                            document.getElementById('edit-item-time').value = data.time;

                            editItemModal.style.display = 'block';
                        })
                        .catch(error => {
                            console.error('Error fetching item details:', error);
                        });
                }
            });
        });

        closeEditModalBtn.addEventListener('click', function() {
            editItemModal.style.display = 'none';
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === editItemModal) {
                editItemModal.style.display = 'none';
            }
        });
    }
});



