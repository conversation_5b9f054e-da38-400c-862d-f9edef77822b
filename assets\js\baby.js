/**
 * Baby page functionality
 * This script handles all interactive elements on the baby page:
 * - Add New Baby button
 * - Grid/List view toggle
 * - Form visibility
 */

// Log when the script is loaded
console.log('Baby.js loaded');

// Main function to initialize all baby page functionality
function initBabyPage() {
    console.log('Initializing baby page functionality');

    // Initialize Add New Baby button
    initAddBabyButton();

    // Initialize view toggle (grid/list)
    initViewToggle();

    // Initialize form close button
    initFormCloseButton();

    // Initialize form cancel button
    initFormCancelButton();

    // Initialize all cancel buttons on the page
    initAllCancelButtons();

    // Add a small delay to catch any dynamically added cancel buttons
    setTimeout(initAllCancelButtons, 500);
}

// Function to initialize the Add New Baby button
function initAddBabyButton() {
    const addBabyBtn = document.querySelector('.add-baby-btn');
    const babyForm = document.querySelector('.baby-form-container');

    console.log('Add Baby Button found:', !!addBabyBtn);
    console.log('Baby Form found:', !!babyForm);

    if (addBabyBtn && babyForm) {
        // Make sure the button is clickable
        addBabyBtn.style.cursor = 'pointer';

        // Add multiple event handlers for maximum reliability

        // 1. Direct onclick property
        addBabyBtn.onclick = function(e) {
            console.log('Add Baby button clicked via onclick property');
            e.preventDefault();
            showBabyForm();
            return false;
        };

        // 2. addEventListener
        addBabyBtn.addEventListener('click', function(e) {
            console.log('Add Baby button clicked via addEventListener');
            e.preventDefault();
            showBabyForm();
            return false;
        });

        // 3. Direct attribute (already added in HTML)

        // 4. Make sure the icon is also clickable
        const icon = addBabyBtn.querySelector('i');
        if (icon) {
            icon.style.pointerEvents = 'none'; // Prevent icon from capturing clicks
        }
    }
}

// Function to initialize the view toggle (grid/list)
function initViewToggle() {
    const gridViewBtn = document.querySelector('.grid-view-btn');
    const listViewBtn = document.querySelector('.list-view-btn');
    const babiesGrid = document.querySelector('.babies-grid');

    console.log('Grid View Button found:', !!gridViewBtn);
    console.log('List View Button found:', !!listViewBtn);
    console.log('Babies Grid found:', !!babiesGrid);

    if (gridViewBtn && listViewBtn && babiesGrid) {
        // Set initial state - grid view is active by default
        babiesGrid.classList.add('grid-view');
        gridViewBtn.classList.add('active');

        // Grid view button click handler
        gridViewBtn.onclick = function() {
            console.log('Grid view button clicked');
            babiesGrid.classList.remove('list-view');
            babiesGrid.classList.add('grid-view');
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');

            // Save preference to localStorage
            localStorage.setItem('babyViewPreference', 'grid');
        };

        // List view button click handler
        listViewBtn.onclick = function() {
            console.log('List view button clicked');
            babiesGrid.classList.remove('grid-view');
            babiesGrid.classList.add('list-view');
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');

            // Save preference to localStorage
            localStorage.setItem('babyViewPreference', 'list');
        };

        // Load saved preference from localStorage
        const savedPreference = localStorage.getItem('babyViewPreference');
        if (savedPreference === 'list') {
            // Trigger list view
            listViewBtn.click();
        }
    }
}

// Function to initialize the form close button
function initFormCloseButton() {
    const closeBtn = document.querySelector('.close-form-btn');
    const babyForm = document.querySelector('.baby-form-container');
    const addBabyBtn = document.querySelector('.add-baby-btn');

    if (closeBtn && babyForm && addBabyBtn) {
        closeBtn.onclick = function(e) {
            e.preventDefault();

            // Hide the form
            babyForm.style.display = 'none';

            // Reset the Add New Baby button
            if (addBabyBtn.tagName.toLowerCase() === 'a') {
                // If it's an anchor tag
                const icon = addBabyBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-plus';
                }
                addBabyBtn.innerHTML = addBabyBtn.innerHTML.replace('Cancel', 'Add New Baby');
            } else {
                // If it's a button
                addBabyBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Baby';
            }

            return false;
        };
    }
}

// Function to initialize the form cancel button
function initFormCancelButton() {
    const cancelBtn = document.querySelector('#cancel-add-baby');
    const babyForm = document.querySelector('.baby-form-container');
    const addBabyBtn = document.querySelector('.add-baby-btn');

    console.log('Cancel button found:', !!cancelBtn);

    if (cancelBtn && babyForm && addBabyBtn) {
        // Use direct onclick handler
        cancelBtn.onclick = function(e) {
            console.log('Cancel button clicked');
            e.preventDefault();

            // Hide the form directly
            babyForm.style.display = 'none';

            // Reset the Add New Baby button
            addBabyBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Baby';

            return false;
        };
    }

    // Also add a direct event listener to all cancel buttons
    document.querySelectorAll('.cancel-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            console.log('Cancel button clicked (from querySelectorAll)');
            e.preventDefault();

            // Hide the form directly
            const form = document.querySelector('.baby-form-container');
            if (form) {
                form.style.display = 'none';

                // Reset the Add New Baby button
                const addBtn = document.querySelector('.add-baby-btn');
                if (addBtn) {
                    addBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Baby';
                }
            }

            return false;
        });
    });
}

// Global function that can be called from HTML
function toggleBabyForm(button) {
    console.log('toggleBabyForm called from HTML');
    const babyForm = document.querySelector('.baby-form-container');

    if (babyForm) {
        if (babyForm.style.display === 'none' || babyForm.style.display === '') {
            // Show the form
            babyForm.style.display = 'block';

            // Change button text
            button.innerHTML = '<i class="fas fa-times"></i> Cancel';
        } else {
            // Hide the form
            babyForm.style.display = 'none';

            // Reset button text
            button.innerHTML = '<i class="fas fa-plus"></i> Add New Baby';
        }
    }
}

// Global function to show the baby form - can be called directly from HTML
function showBabyForm() {
    console.log('showBabyForm called directly from HTML');
    const babyForm = document.querySelector('.baby-form-container');
    const addBabyBtn = document.querySelector('.add-baby-btn');

    if (babyForm) {
        // Show the form
        babyForm.style.display = 'block';

        // Change the Add New Baby button text
        if (addBabyBtn) {
            addBabyBtn.innerHTML = '<i class="fas fa-times"></i> Cancel';
        }

        // Focus on the first input in the form
        const firstInput = babyForm.querySelector('input[type="text"]');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }

    return false;
}

// Global function to hide the baby form - can be called directly from HTML
function hideBabyForm() {
    console.log('hideBabyForm called directly from HTML');
    const babyForm = document.querySelector('.baby-form-container');
    const addBabyBtn = document.querySelector('.add-baby-btn');

    if (babyForm) {
        // Hide the form
        babyForm.style.display = 'none';

        // Reset the Add New Baby button
        if (addBabyBtn) {
            addBabyBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Baby';
        }
    }

    return false;
}

// Function to initialize all cancel buttons on the page
function initAllCancelButtons() {
    console.log('Initializing all cancel buttons');

    // Get all elements with text content containing "Cancel"
    document.querySelectorAll('button, a').forEach(function(element) {
        if (element.textContent.includes('Cancel')) {
            console.log('Found cancel button:', element);

            // Add click handler
            element.onclick = function(e) {
                console.log('Cancel button clicked:', this);
                e.preventDefault();
                hideBabyForm();
                return false;
            };
        }
    });

    // Specifically target the top-right cancel button that might be added by Bootstrap or other frameworks
    const topRightCancelBtn = document.querySelector('.modal-header .close, .modal-header .btn-close, button.close, .btn-success.cancel, button.btn-success[class*="cancel"], a.btn-success[class*="cancel"]');
    if (topRightCancelBtn) {
        console.log('Found top-right cancel button:', topRightCancelBtn);
        topRightCancelBtn.onclick = function(e) {
            console.log('Top-right cancel button clicked');
            e.preventDefault();
            hideBabyForm();
            return false;
        };
    }

    // Target all green cancel buttons (like the one in the screenshot)
    document.querySelectorAll('.btn-success').forEach(function(btn) {
        if (btn.textContent.includes('Cancel')) {
            console.log('Found green cancel button:', btn);
            btn.onclick = function(e) {
                console.log('Green cancel button clicked');
                e.preventDefault();
                hideBabyForm();
                return false;
            };
        }
    });
}

// Add a global click handler for any cancel buttons that might be added dynamically
document.addEventListener('click', function(e) {
    // Check if the clicked element is a cancel button
    if (e.target && (
        (e.target.textContent && e.target.textContent.includes('Cancel')) ||
        (e.target.classList && (e.target.classList.contains('close') || e.target.classList.contains('btn-close'))) ||
        (e.target.parentElement && e.target.parentElement.classList &&
         (e.target.parentElement.classList.contains('close') || e.target.parentElement.classList.contains('btn-close')))
    )) {
        console.log('Cancel button clicked via global handler:', e.target);
        e.preventDefault();
        hideBabyForm();
        return false;
    }

    // Check if the clicked element is the Add New Baby button or its icon
    if (e.target && (
        (e.target.classList && e.target.classList.contains('add-baby-btn')) ||
        (e.target.parentElement && e.target.parentElement.classList && e.target.parentElement.classList.contains('add-baby-btn'))
    )) {
        console.log('Add New Baby button clicked via global handler:', e.target);
        e.preventDefault();

        // If the button contains "Cancel", hide the form
        if (e.target.textContent && e.target.textContent.includes('Cancel')) {
            hideBabyForm();
        } else {
            // Otherwise, show the form
            showBabyForm();
        }

        return false;
    }
});

// Function to check if we need to show the form on page load
function checkShowFormOnLoad() {
    // Check if there's an error message
    const errorMessage = document.querySelector('.message.error');
    if (errorMessage) {
        console.log('Error message found, showing form automatically');
        showBabyForm();
    }

    // Check if the URL has a hash that indicates we should show the form
    if (window.location.hash === '#add-baby' || window.location.hash === '#show-form') {
        console.log('URL hash indicates form should be shown');
        showBabyForm();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initBabyPage();

    // Check if we need to show the form
    setTimeout(checkShowFormOnLoad, 200);
});

// Also try to initialize immediately in case the DOM is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(function() {
        initBabyPage();
        checkShowFormOnLoad();
    }, 100);
}
