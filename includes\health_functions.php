<?php

/**
 * Functions for handling health symptoms and tips
 */

// Include database connection
require_once 'database.php';

/**
 * Get all available symptoms from the Health table
 *
 * @return array Array of symptoms with their IDs and tips
 */
function getAllSymptoms()
{
    global $connection;

    $sql = "SELECT id, symptom_name, tip FROM Health ORDER BY symptom_name";
    $result = $connection->query($sql);

    $symptoms = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $symptoms[] = $row;
        }
    }

    return $symptoms;
}

/**
 * Get a specific symptom by ID
 *
 * @param int $symptomId Symptom ID
 * @return array|null Symptom data or null if not found
 */
function getSymptomById($symptomId)
{
    global $connection;

    $sql = "SELECT id, symptom_name, tip FROM Health WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $symptomId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Log a new symptom for a user
 *
 * @param int $userId User ID
 * @param int $healthId Health symptom ID
 * @param string $severity Symptom severity
 * @param string $dateLogged Date the symptom was logged
 * @param string $notes Additional notes
 * @param int|null $agendaId Optional agenda ID to associate with
 * @return bool True if successful, false otherwise
 */
function logSymptom($userId, $healthId, $severity, $dateLogged, $notes = '', $agendaId = null)
{
    global $connection;

    $sql = "INSERT INTO Symptoms (user_id, health_id, severity, date_logged, notes, agenda_id)
            VALUES (?, ?, ?, ?, ?, ?)";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("iisssi", $userId, $healthId, $severity, $dateLogged, $notes, $agendaId);

    return $stmt->execute();
}

/**
 * Get all symptoms logged by a user
 *
 * @param int $userId User ID
 * @return array Array of logged symptoms with their details
 */
function getUserSymptoms($userId)
{
    global $connection;

    $sql = "SELECT s.id, s.severity, s.date_logged, s.notes, s.agenda_id,
                   h.symptom_name, h.tip
            FROM Symptoms s
            JOIN Health h ON s.health_id = h.id
            WHERE s.user_id = ?
            ORDER BY s.date_logged DESC";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $symptoms = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $symptoms[] = $row;
        }
    }

    return $symptoms;
}

/**
 * Get health tip for a specific symptom
 *
 * @param int $healthId Health symptom ID
 * @return string|null Health tip or null if not found
 */
function getHealthTipForSymptom($healthId)
{
    global $connection;

    $sql = "SELECT tip FROM Health WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $healthId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['tip'];
    }

    return null;
}

/**
 * Update a logged symptom
 *
 * @param int $symptomId Symptom log ID
 * @param int $healthId Health symptom ID
 * @param string $severity Symptom severity
 * @param string $notes Additional notes
 * @return bool True if successful, false otherwise
 */
function updateSymptomLog($symptomId, $healthId, $severity, $notes)
{
    global $connection;

    $sql = "UPDATE Symptoms
            SET health_id = ?, severity = ?, notes = ?
            WHERE id = ?";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("issi", $healthId, $severity, $notes, $symptomId);

    return $stmt->execute();
}

/**
 * Log a new symptom for a user and add it to the agenda
 *
 * @param int $userId User ID
 * @param int $healthId Health symptom ID
 * @param string $severity Symptom severity
 * @param string $dateLogged Date the symptom was logged
 * @param string $notes Additional notes
 * @return int|false New symptom ID if successful, false otherwise
 */
function logSymptomWithAgenda($userId, $healthId, $severity, $dateLogged, $notes = '')
{
    global $connection;

    // Start transaction
    $connection->begin_transaction();

    try {
        // Get symptom details for the agenda item
        $symptom = getSymptomById($healthId);
        if (!$symptom) {
            throw new Exception("Symptom not found");
        }

        // Create agenda item
        $title = "Symptom: " . $symptom['symptom_name'];
        $description = "Intensity: " . $severity;
        if (!empty($notes)) {
            $description .= "\n" . $notes;
        }

        // Add health tip to description
        if (!empty($symptom['tip'])) {
            $description .= "\n\nHealth Tip: " . $symptom['tip'];
        }

        // Format time as noon (12:00)
        $time = "12:00";

        // Insert into Agenda
        $sql = "INSERT INTO Agenda (user_id, title, description, date, time) VALUES (?, ?, ?, ?, ?)";
        $stmt = $connection->prepare($sql);
        $stmt->bind_param("issss", $userId, $title, $description, $dateLogged, $time);
        $stmt->execute();
        $agendaId = $connection->insert_id;

        // Insert into Symptoms with the agenda_id
        $sql = "INSERT INTO Symptoms (user_id, health_id, severity, date_logged, notes, agenda_id)
                VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $connection->prepare($sql);
        $stmt->bind_param("iisssi", $userId, $healthId, $severity, $dateLogged, $notes, $agendaId);
        $stmt->execute();
        $symptomId = $connection->insert_id;

        // Commit transaction
        $connection->commit();

        return $symptomId;
    } catch (Exception $e) {
        // Rollback transaction on error
        $connection->rollback();
        return false;
    }
}

/**
 * Delete a logged symptom and its associated agenda item if exists
 *
 * @param int $symptomId Symptom log ID
 * @return bool True if successful, false otherwise
 */
function deleteSymptomLog($symptomId)
{
    global $connection;

    // Start transaction
    $connection->begin_transaction();

    try {
        // Get the agenda_id associated with this symptom
        $sql = "SELECT agenda_id FROM Symptoms WHERE id = ?";
        $stmt = $connection->prepare($sql);
        $stmt->bind_param("i", $symptomId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $agendaId = $row['agenda_id'];

            // Delete the symptom
            $sql = "DELETE FROM Symptoms WHERE id = ?";
            $stmt = $connection->prepare($sql);
            $stmt->bind_param("i", $symptomId);
            $stmt->execute();

            // If there's an associated agenda item, delete it too
            if ($agendaId) {
                $sql = "DELETE FROM Agenda WHERE id = ?";
                $stmt = $connection->prepare($sql);
                $stmt->bind_param("i", $agendaId);
                $stmt->execute();
            }

            // Commit transaction
            $connection->commit();
            return true;
        } else {
            // Symptom not found
            $connection->rollback();
            return false;
        }
    } catch (Exception $e) {
        // Rollback transaction on error
        $connection->rollback();
        return false;
    }
}
