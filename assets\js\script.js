// Hamburger Menu and Login Modal
document.addEventListener('DOMContentLoaded', function() {
    // Fix for all Remember Me checkboxes
    const allRememberMeCheckboxes = document.querySelectorAll('input[type="checkbox"][name="remember_me"]');
    allRememberMeCheckboxes.forEach(checkbox => {
        // Add event listener to handle the checkbox state
        checkbox.addEventListener('change', function() {
            console.log('Remember me checkbox changed:', this.checked);

            // Store the checkbox state in localStorage to persist it
            localStorage.setItem('remember_me_' + (this.form ? this.form.id : 'unknown'), this.checked);
        });

        // Restore checkbox state from localStorage if available
        const formId = checkbox.form ? checkbox.form.id : 'unknown';
        const savedState = localStorage.getItem('remember_me_' + formId);
        if (savedState === 'true') {
            checkbox.checked = true;
        }
    });

    // Handle all forms with remember_me checkbox
    const allForms = document.querySelectorAll('form');
    allForms.forEach(form => {
        // Check if the form has a remember_me checkbox
        const rememberCheckbox = form.querySelector('input[name="remember_me"]');
        if (rememberCheckbox) {
            // Mark the form as having a remember_me checkbox
            form.setAttribute('data-has-remember-me', 'true');

            // Add submit event listener
            form.addEventListener('submit', function(e) {
                // Only handle forms that don't already have a submit handler
                if (!form.hasAttribute('data-has-submit-handler')) {
                    e.preventDefault();
                    console.log('Generic form with remember_me submitted:', form);

                    // Get form data
                    const formData = new FormData(form);

                    // Special handling for remember_me checkbox
                    if (rememberCheckbox.checked) {
                        formData.set('remember_me', 'on');
                        console.log('Remember me is checked in generic form');
                    }

                    // Submit the form
                    fetch(form.action, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Form submission response:', data);
                        if (data.success && data.redirect) {
                            window.location.href = data.redirect;
                        }
                    })
                    .catch(error => {
                        console.error('Form submission error:', error);
                    });
                }
            });
        }
    });

    // Hamburger Menu
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger && navLinks) {
        // Make sure hamburger is clickable
        hamburger.style.cursor = 'pointer';
        
        hamburger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling
            
            // Toggle active class
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
            
            console.log('Hamburger clicked, nav-links active:', navLinks.classList.contains('active'));
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (hamburger && 
                navLinks.classList.contains('active') && 
                !hamburger.contains(event.target) && 
                !navLinks.contains(event.target)) {
                
                hamburger.classList.remove('active');
                navLinks.classList.remove('active');
            }
        });
    } else {
        console.error('Hamburger menu elements not found');
    }

    // Login and Registration Modals
    const loginModal = document.getElementById('login-modal');
    const registerModal = document.getElementById('register-modal');
    const loginLinks = document.querySelectorAll('.login-trigger'); // Changed from ID to class
    const registerLink = document.getElementById('register-link');
    const loginLinkFromRegister = document.getElementById('login-link-from-register');
    const closeBtns = document.querySelectorAll('.close');

    // Function to open a modal
    function openModal(modal) {
        // Close all modals first
        closeAllModals();

        // Open the specified modal
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
        }
    }

    // Function to close all modals
    function closeAllModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
        document.body.style.overflow = ''; // Re-enable scrolling
    }

    // Open login modal when login button is clicked
    if (loginLinks.length > 0) {
        loginLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                openModal(loginModal);
            });
        });
    }

    // Open registration modal when register link is clicked
    if (registerLink) {
        registerLink.addEventListener('click', function(e) {
            e.preventDefault();
            openModal(registerModal);
        });
    }

    // Switch from registration to login modal
    if (loginLinkFromRegister) {
        loginLinkFromRegister.addEventListener('click', function(e) {
            e.preventDefault();
            openModal(loginModal);
        });
    }

    // Close modal when X is clicked
    if (closeBtns) {
        closeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                closeAllModals();
            });
        });
    }

    // Close modal when clicking outside of it
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            closeAllModals();
        }
    });

    // Function to handle form submission (both login and registration)
    function handleFormSubmission(form, messageElement, loadingText, successCallback) {
        if (!form || !messageElement) {
            console.error('Form or message element not found:', { form, messageElement });
            return;
        }

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted:', form.id);

            // Get form data
            const formData = new FormData(form);

            // Special handling for remember_me checkbox - works with any form
            const rememberCheckbox = form.querySelector('input[name="remember_me"]');
            if (rememberCheckbox) {
                // Explicitly set the value for the remember_me checkbox
                // FormData will only include checked checkboxes
                if (rememberCheckbox.checked) {
                    formData.set('remember_me', 'on');
                }
                console.log('Remember me checkbox state:', rememberCheckbox.checked);
            }

            // Log form data for debugging
            console.log('Form data:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + (pair[0] === 'password' ? '********' : pair[1]));
            }

            // Disable form elements during submission
            const formElements = form.elements;
            for (let i = 0; i < formElements.length; i++) {
                formElements[i].disabled = true;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.textContent = loadingText;
            submitBtn.classList.add('loading');

            // Clear previous messages
            messageElement.style.display = 'none';
            messageElement.textContent = '';
            messageElement.className = 'message';

            console.log('Sending request to:', form.action);

            // Send AJAX request
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    console.error('Response not OK:', response.statusText);
                }
                return response.json().catch(error => {
                    console.error('Error parsing JSON:', error);
                    throw new Error('Invalid JSON response');
                });
            })
            .then(data => {
                console.log('Response data:', data);

                // Display message
                messageElement.textContent = data.message || 'Unknown response from server';
                messageElement.classList.add(data.success ? 'success' : 'error');
                messageElement.style.display = 'block';

                if (data.success) {
                    if (successCallback) {
                        successCallback(data);
                    }
                } else {
                    // Re-enable form elements if submission failed
                    for (let i = 0; i < formElements.length; i++) {
                        formElements[i].disabled = false;
                    }
                    submitBtn.textContent = originalBtnText;
                    submitBtn.classList.remove('loading');
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                messageElement.textContent = 'An error occurred. Please try again.';
                messageElement.classList.add('error');
                messageElement.style.display = 'block';

                // Re-enable form elements
                for (let i = 0; i < formElements.length; i++) {
                    formElements[i].disabled = false;
                }
                submitBtn.textContent = originalBtnText;
                submitBtn.classList.remove('loading');
            });
        });
    }

    // Handle login form submission
    const loginForm = document.getElementById('login-form');
    const loginMessage = document.getElementById('login-message');

    if (loginForm) {
        // Direct form submission for login to ensure checkbox is properly handled
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Login form submitted');

            // Get form data
            const formData = new FormData(loginForm);

            // Special handling for remember_me checkbox - works with any login form
            const rememberCheckbox = loginForm.querySelector('input[name="remember_me"]');
            if (rememberCheckbox) {
                // Explicitly set the value for the remember_me checkbox
                if (rememberCheckbox.checked) {
                    formData.set('remember_me', 'on');
                    console.log('Remember me is checked');
                    // Save the state to localStorage
                    localStorage.setItem('remember_me_login-form', 'true');
                } else {
                    console.log('Remember me is NOT checked');
                    // Remove the remember_me from localStorage
                    localStorage.setItem('remember_me_login-form', 'false');
                }
            }

            // Log form data for debugging
            console.log('Login form data:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + (pair[0] === 'password' ? '********' : pair[1]));
            }

            // Disable form elements during submission
            const formElements = loginForm.elements;
            for (let i = 0; i < formElements.length; i++) {
                formElements[i].disabled = true;
            }

            // Show loading state
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.textContent = 'Logging in...';
            submitBtn.classList.add('loading');

            // Clear previous messages
            loginMessage.style.display = 'none';
            loginMessage.textContent = '';
            loginMessage.className = 'message';

            console.log('Sending login request to:', loginForm.action);

            // Send AJAX request
            fetch(loginForm.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Login response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Login response data:', data);

                // Display message
                loginMessage.textContent = data.message || 'Unknown response from server';
                loginMessage.classList.add(data.success ? 'success' : 'error');
                loginMessage.style.display = 'block';

                if (data.success) {
                    // Redirect after a short delay if login was successful
                    setTimeout(() => {
                        window.location.href = data.redirect || window.location.href;
                    }, 1500);
                } else {
                    // Re-enable form elements if login failed
                    for (let i = 0; i < formElements.length; i++) {
                        formElements[i].disabled = false;
                    }
                    submitBtn.textContent = originalBtnText;
                    submitBtn.classList.remove('loading');
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                loginMessage.textContent = 'An error occurred. Please try again.';
                loginMessage.classList.add('error');
                loginMessage.style.display = 'block';

                // Re-enable form elements
                for (let i = 0; i < formElements.length; i++) {
                    formElements[i].disabled = false;
                }
                submitBtn.textContent = originalBtnText;
                submitBtn.classList.remove('loading');
            });
        });
    }

    // Handle registration form submission
    const registerForm = document.getElementById('register-form');
    const registerMessage = document.getElementById('register-message');

    // Password confirmation validation
    if (registerForm) {
        const password = document.getElementById('reg-password');
        const confirmPassword = document.getElementById('reg-confirm-password');

        confirmPassword.addEventListener('input', function() {
            if (password.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        });

        password.addEventListener('input', function() {
            if (confirmPassword.value) {
                if (password.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Passwords do not match');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
        });

        // Direct form submission for registration
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Registration form submitted');

            // Get form data
            const formData = new FormData(registerForm);

            // Log form data for debugging (excluding passwords)
            console.log('Form data:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + (pair[0].includes('password') ? '********' : pair[1]));
            }

            // Disable form elements during submission
            const formElements = registerForm.elements;
            for (let i = 0; i < formElements.length; i++) {
                formElements[i].disabled = true;
            }

            // Show loading state
            const submitBtn = registerForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.textContent;
            submitBtn.textContent = 'Creating account...';
            submitBtn.classList.add('loading');

            // Clear previous messages
            registerMessage.style.display = 'none';
            registerMessage.textContent = '';
            registerMessage.className = 'message';

            console.log('Sending registration request to:', registerForm.action);

            // Send AJAX request
            fetch(registerForm.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Registration response status:', response.status);
                return response.text().then(text => {
                    console.log('Raw response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Error parsing JSON:', e);
                        throw new Error('Invalid JSON response: ' + text);
                    }
                });
            })
            .then(data => {
                console.log('Registration response data:', data);

                // Display message
                registerMessage.textContent = data.message || 'Unknown response from server';
                registerMessage.classList.add(data.success ? 'success' : 'error');
                registerMessage.style.display = 'block';

                if (data.success) {
                    // Redirect after a short delay if registration was successful
                    setTimeout(() => {
                        window.location.href = data.redirect || window.location.href;
                    }, 1500);
                } else {
                    // Re-enable form elements if registration failed
                    for (let i = 0; i < formElements.length; i++) {
                        formElements[i].disabled = false;
                    }
                    submitBtn.textContent = originalBtnText;
                    submitBtn.classList.remove('loading');
                }
            })
            .catch(error => {
                console.error('Registration error:', error);
                registerMessage.textContent = 'An error occurred: ' + error.message;
                registerMessage.classList.add('error');
                registerMessage.style.display = 'block';

                // Re-enable form elements
                for (let i = 0; i < formElements.length; i++) {
                    formElements[i].disabled = false;
                }
                submitBtn.textContent = originalBtnText;
                submitBtn.classList.remove('loading');
            });
        });
    }
});

// Slideshow
let slideIndex = 0;
const slides = document.querySelectorAll('.slide');
const dots = document.querySelectorAll('.dot');
let timeoutId = null;

function showSlides() {
    // Clear any existing timeout
    if (timeoutId) {
        clearTimeout(timeoutId);
    }

    // Hide all slides
    slides.forEach(slide => {
        slide.classList.remove('active', 'fadeIn');
        slide.classList.add('fadeOut');
    });

    // Remove active class from all dots
    dots.forEach(dot => dot.classList.remove('active'));

    // Increment slideIndex
    slideIndex++;
    if (slideIndex > slides.length) {slideIndex = 1}

    // Show current slide with fade in
    const currentSlide = slides[slideIndex-1];
    currentSlide.style.display = "block";

    // Small delay to ensure proper transition
    setTimeout(() => {
        currentSlide.classList.remove('fadeOut');
        currentSlide.classList.add('active', 'fadeIn');
    }, 50);

    dots[slideIndex-1].classList.add('active');

    // Set timer for next slide
    timeoutId = setTimeout(showSlides, 5000); // Change slide every 5 seconds
}

// Navigation arrows
document.querySelector('.prev').addEventListener('click', (e) => {
    e.preventDefault();
    slideIndex = slideIndex <= 1 ? slides.length : slideIndex - 2;
    showSlides();
});

document.querySelector('.next').addEventListener('click', (e) => {
    e.preventDefault();
    showSlides();
});

// Dot navigation
dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
        slideIndex = index;
        showSlides();
    });
});

// Initialize slideshow
showSlides();

// Baby Names Search Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get the search form elements
    const searchForm = document.querySelector('.baby-names-form');
    const searchInput = document.getElementById('baby-name');
    const genderSelect = document.getElementById('name-gender');
    const meaningInput = document.getElementById('name-meaning');
    const noResultsDiv = document.getElementById('no-results');

    // Get the name cards
    const nameCards = document.querySelectorAll('.name-card');

    // Add event listener for form submission
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            filterNames();
        });
    }

    // Add event listener for real-time filtering as user types
    if (searchInput) {
        searchInput.addEventListener('input', filterNames);
    }

    // Add event listeners for select dropdowns
    if (genderSelect) {
        genderSelect.addEventListener('change', filterNames);
    }

    if (meaningInput) {
        meaningInput.addEventListener('input', filterNames);
    }

    // Function to filter names based on search criteria
    function filterNames() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const selectedGender = genderSelect ? genderSelect.value.toLowerCase() : 'all';
        const meaningTerm = meaningInput ? meaningInput.value.toLowerCase() : '';

        let visibleCount = 0;

        // Loop through all name cards
        nameCards.forEach(card => {
            const nameElement = card.querySelector('h4');
            const genderElement = card.querySelector('.gender');
            const meaningElement = card.querySelector('p:nth-of-type(2)');

            if (!nameElement || !meaningElement || !genderElement) {
                return; // Skip if any element is missing
            }

            const name = nameElement.textContent.toLowerCase();
            const genderText = genderElement.textContent.toLowerCase();

            // Extract meaning text properly
            const meaningFullText = meaningElement.textContent.toLowerCase();
            let meaningText = meaningFullText;

            // Try to extract the meaning value after the colon
            if (meaningFullText.includes(':')) {
                meaningText = meaningFullText.split(':')[1].trim();
            }

            // Check if the card matches all criteria
            const nameMatch = searchTerm === '' || name.includes(searchTerm);

            // For gender matching - exact match
            let genderMatch = true;
            if (selectedGender !== 'all') {
                genderMatch = genderText === selectedGender;
            }

            const meaningMatch = meaningTerm === '' || meaningText.includes(meaningTerm);

            // Show or hide based on matches
            if (nameMatch && genderMatch && meaningMatch) {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // Show or hide the 'no results' message
        if (noResultsDiv) {
            noResultsDiv.style.display = visibleCount === 0 ? 'block' : 'none';
        }
    }
});

// Baby page functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - checking for baby page elements');

    // Add New Baby button functionality
    const addBabyBtn = document.querySelector('.add-baby-btn');
    const babyForm = document.querySelector('.baby-form-container');

    console.log('Add Baby Button found:', !!addBabyBtn);
    console.log('Baby Form found:', !!babyForm);

    if (addBabyBtn && babyForm) {
        console.log('Setting up click event listener for Add Baby button');

        // Make sure the button is clickable
        addBabyBtn.style.cursor = 'pointer';

        addBabyBtn.addEventListener('click', function(e) {
            console.log('Add Baby button clicked');
            e.preventDefault(); // Prevent any default behavior

            // Toggle form visibility
            if (babyForm.style.display === 'none' || !babyForm.style.display) {
                console.log('Showing baby form');
                babyForm.style.display = 'block';
                addBabyBtn.innerHTML = '<i class="fas fa-times"></i> Cancel';
            } else {
                console.log('Hiding baby form');
                babyForm.style.display = 'none';
                addBabyBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Baby';
            }
        });
    }
});

// Debug hamburger menu
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.nav-links');

    if (hamburger && navLinks) {
        // Log initial state
        console.log('Hamburger menu initialized');
        console.log('Nav links visibility:', getComputedStyle(navLinks).display);
        console.log('Nav links background color:', getComputedStyle(navLinks).backgroundColor);
        
        // Check all nav links
        const links = navLinks.querySelectorAll('a');
        console.log('Number of nav links:', links.length);
        links.forEach((link, index) => {
            console.log(`Link ${index + 1}:`, link.textContent);
            console.log(`Link ${index + 1} color:`, getComputedStyle(link).color);
            console.log(`Link ${index + 1} background:`, getComputedStyle(link).backgroundColor);
        });
        
        // Enhanced click handler
        hamburger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Toggle active class
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
            
            // Log state after click
            console.log('Hamburger clicked');
            console.log('Nav links active:', navLinks.classList.contains('active'));
            console.log('Nav links display after click:', getComputedStyle(navLinks).display);
            
            // Force repaint
            navLinks.style.display = 'none';
            navLinks.offsetHeight; // Force reflow
            navLinks.style.display = navLinks.classList.contains('active') ? 'flex' : 'none';
        });
    }
});
