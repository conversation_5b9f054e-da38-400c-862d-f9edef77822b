<?php

/**
 * Functions for handling baby information
 */

// Include database connection
require_once 'database.php';

/**
 * Get user's pregnancies
 *
 * @param int $userId User ID
 * @return array Array of pregnancies
 */
function getUserPregnancies($userId)
{
    global $connection;

    // Check if title column exists
    $checkColumnSql = "SHOW COLUMNS FROM Pregnancy LIKE 'title'";
    $columnResult = $connection->query($checkColumnSql);
    $hasTitleColumn = ($columnResult && $columnResult->num_rows > 0);

    if ($hasTitleColumn) {
        $sql = "SELECT id, title, start_date, due_date, notes
                FROM Pregnancy
                WHERE user_id = ?
                ORDER BY due_date DESC";
    } else {
        $sql = "SELECT id, start_date, due_date, notes
                FROM Pregnancy
                WHERE user_id = ?
                ORDER BY due_date DESC";
    }

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $pregnancies = [];
    while ($row = $result->fetch_assoc()) {
        // If no title column or title is empty, create a default title
        if (!$hasTitleColumn || empty($row['title'])) {
            $row['title'] = 'Pregnancy due ' . $row['due_date'];
        }
        $pregnancies[] = $row;
    }

    return $pregnancies;
}

/**
 * Get all available baby genders
 *
 * @return array Array of baby genders
 */
function getAllBabyGenders()
{
    global $connection;

    $sql = "SELECT id, gender_name, description FROM BabyGender ORDER BY id";
    $result = $connection->query($sql);

    $genders = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $genders[] = $row;
        }
    }

    return $genders;
}

/**
 * Get baby gender by ID
 *
 * @param int $genderId Gender ID
 * @return array|null Gender data or null if not found
 */
function getBabyGenderById($genderId)
{
    global $connection;

    $sql = "SELECT id, gender_name, description FROM BabyGender WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $genderId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Get user's babies
 *
 * @param int $userId User ID
 * @return array Array of babies
 */
function getUserBabies($userId)
{
    global $connection;

    $sql = "SELECT b.id, b.name, b.birth_date, b.weight, b.height, b.pregnancy_id,
                   bg.gender_name, bn.name as chosen_name, bn.origin, bn.meaning
            FROM Baby b
            LEFT JOIN BabyGender bg ON b.gender_id = bg.id
            LEFT JOIN BabyNames bn ON b.baby_name_id = bn.id
            WHERE b.user_id = ?
            ORDER BY b.birth_date DESC";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $babies = [];
    while ($row = $result->fetch_assoc()) {
        $babies[] = $row;
    }

    return $babies;
}

/**
 * Get baby by ID
 *
 * @param int $babyId Baby ID
 * @return array|null Baby data or null if not found
 */
function getBabyById($babyId)
{
    global $connection;

    $sql = "SELECT b.id, b.user_id, b.name, b.gender_id, b.birth_date, b.weight, b.height,
                   b.pregnancy_id, b.baby_name_id, bg.gender_name
            FROM Baby b
            LEFT JOIN BabyGender bg ON b.gender_id = bg.id
            WHERE b.id = ?";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $babyId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Create a new baby
 *
 * @param int $userId User ID
 * @param string $name Baby name
 * @param int $genderId Gender ID
 * @param string $birthDate Birth date
 * @param float $weight Weight
 * @param float $height Height
 * @param int|null $pregnancyId Pregnancy ID
 * @param int|null $babyNameId Baby name ID
 * @return int|false New baby ID or false on failure
 */
function createBaby($userId, $name, $genderId, $birthDate, $weight, $height, $pregnancyId = null, $babyNameId = null)
{
    global $connection;

    // Convert empty string to null for pregnancy_id
    if ($pregnancyId === '' || $pregnancyId === false || $pregnancyId === 0) {
        $pregnancyId = null;
    }

    // Convert empty string to null for baby_name_id
    if ($babyNameId === '' || $babyNameId === false || $babyNameId === 0) {
        $babyNameId = null;
    }

    $sql = "INSERT INTO Baby (user_id, name, gender_id, birth_date, weight, height, pregnancy_id, baby_name_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("isisddii", $userId, $name, $genderId, $birthDate, $weight, $height, $pregnancyId, $babyNameId);

    try {
        if ($stmt->execute()) {
            return $connection->insert_id;
        }
    } catch (mysqli_sql_exception $e) {
        // Log the error for debugging
        error_log("Error creating baby: " . $e->getMessage());

        // Check if it's a foreign key constraint error
        if ($e->getCode() == 1452) {
            error_log("Foreign key constraint error. Pregnancy ID: " . ($pregnancyId ?? 'NULL'));
        }
    }

    return false;
}

/**
 * Update baby information
 *
 * @param int $babyId Baby ID
 * @param string $name Baby name
 * @param int $genderId Gender ID
 * @param string $birthDate Birth date
 * @param float $weight Weight
 * @param float $height Height
 * @param int|null $pregnancyId Pregnancy ID
 * @param int|null $babyNameId Baby name ID
 * @return bool True if successful, false otherwise
 */
function updateBaby($babyId, $name, $genderId, $birthDate, $weight, $height, $pregnancyId = null, $babyNameId = null)
{
    global $connection;

    // Convert empty string to null for pregnancy_id
    if ($pregnancyId === '' || $pregnancyId === false || $pregnancyId === 0) {
        $pregnancyId = null;
    }

    // Convert empty string to null for baby_name_id
    if ($babyNameId === '' || $babyNameId === false || $babyNameId === 0) {
        $babyNameId = null;
    }

    $sql = "UPDATE Baby
            SET name = ?, gender_id = ?, birth_date = ?, weight = ?, height = ?,
                pregnancy_id = ?, baby_name_id = ?
            WHERE id = ?";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("sisddiii", $name, $genderId, $birthDate, $weight, $height, $pregnancyId, $babyNameId, $babyId);

    try {
        return $stmt->execute();
    } catch (mysqli_sql_exception $e) {
        // Log the error for debugging
        error_log("Error updating baby: " . $e->getMessage());

        // Check if it's a foreign key constraint error
        if ($e->getCode() == 1452) {
            error_log("Foreign key constraint error. Pregnancy ID: " . ($pregnancyId ?? 'NULL'));
        }
        return false;
    }
}

/**
 * Delete a baby
 *
 * @param int $babyId Baby ID
 * @return bool True if successful, false otherwise
 */
function deleteBaby($babyId)
{
    global $connection;

    $sql = "DELETE FROM Baby WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $babyId);

    return $stmt->execute();
}

/**
 * Search for baby names
 *
 * @param string|null $name Name to search for
 * @param string|null $gender Gender to filter by
 * @param string|null $origin Origin to filter by (removed from functionality)
 * @param string|null $meaning Meaning to search for
 * @return array Array of matching baby names
 */
function searchBabyNames($name = null, $gender = null, $origin = null, $meaning = null)
{
    global $connection;

    // Build the query
    $conditions = [];
    $params = [];
    $types = "";

    if (!empty($name)) {
        $conditions[] = "name LIKE ?";
        $params[] = "%$name%";
        $types .= "s";
    }

    if (!empty($gender) && $gender !== 'all') {
        $conditions[] = "LOWER(gender) = LOWER(?)";
        $params[] = $gender;
        $types .= "s";
    }

    // Origin filtering removed

    if (!empty($meaning)) {
        $conditions[] = "meaning LIKE ?";
        $params[] = "%$meaning%";
        $types .= "s";
    }

    $sql = "SELECT * FROM BabyNames";

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $sql .= " ORDER BY name LIMIT 50";

    // Execute the query
    $stmt = $connection->prepare($sql);

    if (!$stmt) {
        error_log("Error preparing statement: " . $connection->error);
        return [];
    }

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    if (!$stmt->execute()) {
        error_log("Error executing statement: " . $stmt->error);
        return [];
    }

    $result = $stmt->get_result();

    $names = [];
    while ($row = $result->fetch_assoc()) {
        $names[] = $row;
    }

    return $names;
}

/**
 * Get baby name by ID
 *
 * @param int $nameId Baby name ID
 * @return array|null Baby name data or null if not found
 */
function getBabyNameById($nameId)
{
    global $connection;

    $sql = "SELECT * FROM BabyNames WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $nameId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Populate baby names table with sample data if empty
 */
function populateBabyNamesIfEmpty()
{
    global $connection;

    // Check if table is empty
    $checkSql = "SELECT COUNT(*) as count FROM BabyNames";
    $result = $connection->query($checkSql);
    $row = $result->fetch_assoc();

    if ($row['count'] == 0) {
        // Table is empty, add sample data
        $sampleNames = [
            ['Emma', 'girl', 'german', 'Universal'],
            ['Liam', 'boy', 'irish', 'Strong-willed warrior'],
            ['Sophia', 'girl', 'greek', 'Wisdom'],
            ['Noah', 'boy', 'english', 'Rest, comfort'],
            ['Olivia', 'girl', 'latin', 'Olive tree'],
            ['William', 'boy', 'german', 'Resolute protector'],
            ['Ava', 'girl', 'latin', 'Life, bird-like'],
            ['James', 'boy', 'english', 'Supplanter'],
            ['Isabella', 'girl', 'italian', 'Devoted to God'],
            ['Oliver', 'boy', 'latin', 'Olive tree'],
            ['Mia', 'girl', 'italian', 'Mine, beloved'],
            ['Benjamin', 'boy', 'hebrew', 'Son of the right hand'],
            ['Charlotte', 'girl', 'french', 'Free woman'],
            ['Elijah', 'boy', 'hebrew', 'Yahweh is God'],
            ['Amelia', 'girl', 'german', 'Work, industrious']
        ];

        $insertSql = "INSERT INTO BabyNames (name, gender, origin, meaning) VALUES (?, ?, ?, ?)";
        $stmt = $connection->prepare($insertSql);

        foreach ($sampleNames as $name) {
            $stmt->bind_param("ssss", $name[0], $name[1], $name[2], $name[3]);
            $stmt->execute();
        }

        return true;
    }

    return false;
}

/**
 * Fix inconsistencies in the baby names database
 * This function standardizes origins and other fields
 */
function fixBabyNamesDatabase()
{
    global $connection;

    // Get all names
    $sql = "SELECT id, origin FROM BabyNames";
    $result = $connection->query($sql);

    if (!$result) {
        error_log("Error querying BabyNames: " . $connection->error);
        return false;
    }

    $updateCount = 0;

    while ($row = $result->fetch_assoc()) {
        $id = $row['id'];
        $origin = $row['origin'];

        // Standardize the origin (trim whitespace, lowercase)
        $standardizedOrigin = strtolower(trim($origin));

        // Update if different
        if ($standardizedOrigin !== $origin) {
            $updateSql = "UPDATE BabyNames SET origin = ? WHERE id = ?";
            $stmt = $connection->prepare($updateSql);
            $stmt->bind_param("si", $standardizedOrigin, $id);

            if ($stmt->execute()) {
                $updateCount++;
            } else {
                error_log("Error updating name ID $id: " . $stmt->error);
            }
        }
    }

    error_log("Fixed $updateCount names in the database");
    return true;
}

/**
 * Add Latin baby names to the database
 * This function ensures there are Latin names in the database
 */
function addLatinBabyNames()
{
    global $connection;

    // Check if we already have Latin names
    $checkSql = "SELECT COUNT(*) as count FROM BabyNames WHERE LOWER(origin) = 'latin'";
    $result = $connection->query($checkSql);

    if ($result && $result->fetch_assoc()['count'] > 0) {
        error_log("Latin names already exist in the database");
        return true;
    }

    // Add Latin names
    $latinNames = [
        ['Olivia', 'girl', 'latin', 'Olive tree'],
        ['Oliver', 'boy', 'latin', 'Olive tree'],
        ['Mia', 'girl', 'latin', 'Mine, beloved'],
        ['Lucas', 'boy', 'latin', 'Light-giving, illumination'],
        ['Ava', 'girl', 'latin', 'Life, bird-like'],
        ['Marcus', 'boy', 'latin', 'Dedicated to Mars'],
        ['Julia', 'girl', 'latin', 'Youthful, downy'],
        ['Victor', 'boy', 'latin', 'Conqueror'],
        ['Lucia', 'girl', 'latin', 'Light'],
        ['Felix', 'boy', 'latin', 'Happy, fortunate']
    ];

    $insertSql = "INSERT INTO BabyNames (name, gender, origin, meaning) VALUES (?, ?, ?, ?)";
    $stmt = $connection->prepare($insertSql);

    if (!$stmt) {
        error_log("Error preparing statement: " . $connection->error);
        return false;
    }

    foreach ($latinNames as $name) {
        $stmt->bind_param("ssss", $name[0], $name[1], $name[2], $name[3]);
        if (!$stmt->execute()) {
            error_log("Error inserting Latin name {$name[0]}: " . $stmt->error);
        } else {
            error_log("Added Latin name: {$name[0]}");
        }
    }

    error_log("Added " . count($latinNames) . " Latin names to the database");
    return true;
}
