.calendar-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px; /* Reduced from 20px to make it more compact */
  margin-bottom: 20px; /* Reduced from 30px */
  width: 100%;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px; /* Reduced from 20px */
  width: 100%;
}

.calendar-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-month {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

.nav-arrow {
  color: #6b8e23;
  font-size: 1.2rem;
  text-decoration: none;
}

.add-btn {
  background-color: #c2d8c2;
  color: #333;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
}

.today-btn {
  background-color: #c2d8c2;
  color: #333;
  border: none;
  padding: 5px 15px;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 0.9rem;
}

.calendar {
  border: 1px solid #eee;
  border-radius: 5px;
  overflow: hidden;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 1px solid #eee;
}

.calendar-day-header {
  padding: 8px 5px; /* Reduced from 10px to make it more compact */
  text-align: center;
  font-weight: bold;
  border-right: 1px solid #eee;
  background-color: #f5f5f5;
}

.calendar-day-header:last-child {
  border-right: none;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-bottom: 1px solid #eee;
}

.calendar-week:last-child {
  border-bottom: none;
}

.calendar-day {
  min-height: 90px; /* Reduced from 120px to make it shorter */
  padding: 8px;
  border-right: 1px solid #eee;
  position: relative;
  background-color: white;
}

.calendar-day:last-child {
  border-right: none;
}

.calendar-day.other-month {
  background-color: #f9f9f9;
  color: #aaa;
}

.calendar-day.today {
  background-color: rgba(194, 216, 194, 0.3);
}

.calendar-day.selected {
  background-color: rgba(194, 216, 194, 0.5);
  border: 2px solid #6b8e23;
}

.day-number {
  font-weight: bold;
  margin-bottom: 2px; /* Reduced from 5px */
  text-align: right;
  font-size: 0.9rem; /* Added to make it slightly smaller */
}

.today .day-number {
  color: #6b8e23;
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 3px; /* Reduced from 5px */
  margin-top: 3px; /* Added to create some space after day number */
}

.event {
  padding: 4px; /* Reduced from 5px */
  border-radius: 3px; /* Reduced from 4px */
  font-size: 0.75rem; /* Reduced from 0.8rem */
  color: white;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

.event.appointment {
  background-color: #6b8e23; /* Main olive green */
}

.event.symptom {
  background-color: #8fbc8f; /* Dark sea green - a medium green shade */
}

.event.manual {
  background-color: #c2d8c2; /* Pale green from navbar */
  color: #333; /* Darker text for better contrast on light background */
}

.event-tooltip {
  display: none;
  position: absolute;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  width: 200px;
  top: 100%;
  left: 0;
}

.event:hover .event-tooltip {
  display: block;
}

/* List View Styles */
.agenda-list {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.agenda-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.agenda-item:last-child {
  border-bottom: none;
}

.agenda-date {
  min-width: 100px;
  text-align: center;
  padding-right: 20px;
  border-right: 1px solid #f0f0f0;
}

.agenda-date .day {
  font-size: 1.5rem;
  font-weight: bold;
  color: #6b8e23;
}

.agenda-date .month {
  font-size: 0.9rem;
  color: #666;
}

.agenda-time {
  min-width: 80px;
  text-align: center;
  padding: 0 20px;
}

.agenda-details {
  flex: 1;
  padding: 0 20px;
}

.agenda-details h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.agenda-details p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.agenda-baby {
  color: #6b8e23;
  font-style: italic;
  font-size: 0.9rem;
  margin-top: 5px;
}

.agenda-source {
  display: inline-block;
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 10px;
  margin-top: 5px;
  color: white; /* Default text color - will be overridden for light backgrounds */
}

.source-appointment {
  background-color: #6b8e23; /* Main olive green */
}

.source-symptom {
  background-color: #8fbc8f; /* Dark sea green - a medium green shade */
}

.source-manual {
  background-color: #c2d8c2; /* Pale green from navbar */
  color: #333; /* Darker text for better contrast on light background */
}

/* Section Header */
.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  text-align: center;
  width: 100%;
}

.section-header h2 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.section-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  width: 100%;
}

/* View Toggle and Add Button Styles */
.view-toggle {
  display: flex;
  gap: 10px;
  align-items: center;
}

.view-toggle button,
.add-btn {
  background-color: #f0f0f0;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 1rem;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.view-toggle button.active {
  background-color: #c2d8c2;
  color: #333;
}

.add-btn {
  background-color: #c2d8c2;
  color: #333;
  white-space: nowrap;
}

/* Apply the same hover effect to all buttons */
.view-toggle button:hover,
.add-btn:hover {
  background-color: #b2d8b2;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Keep active button styling even on hover */
.view-toggle button.active:hover {
  background-color: #b2d8b2;
}

/* Section Controls Layout */
.section-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
}

/* View Toggle Layout */
.view-toggle {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
  .calendar {
    display: none;
  }

  .agenda-list {
    display: block;
  }

  .view-toggle .calendar-view-btn {
    display: none;
  }

  .agenda-layout {
    flex-direction: column;
  }

  .date-list-container {
    width: 100%;
    max-height: none;
    margin-bottom: 20px;
  }

  .date-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .date-item {
    width: calc(50% - 5px);
  }
}

.agenda-actions {
  display: flex;
  gap: 10px;
}

.agenda-actions button {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  transition: color 0.3s;
}

.agenda-actions button:hover {
  color: #333;
}

.agenda-actions .edit-btn:hover {
  color: #6b8e23;
}

.agenda-actions .delete-btn:hover {
  color: #e74c3c;
}

.empty-agenda {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-agenda i {
  font-size: 3rem;
  margin-bottom: 10px;
  color: #ddd;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  position: relative;
}

.close {
  position: absolute;
  right: 20px;
  top: 15px;
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #333;
}

.modal h2 {
  margin-top: 0;
  color: #333;
  margin-bottom: 20px;
}

.modal .form-group {
  margin-bottom: 15px;
}

.modal label {
  display: block;
  margin-bottom: 5px;
  color: #555;
}

.modal input[type="text"],
.modal input[type="date"],
.modal input[type="time"],
.modal textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.modal textarea {
  resize: vertical;
  min-height: 80px;
}

.modal .btn {
  background-color: #c2d8c2;
  color: #333;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 10px;
}

.modal .btn:hover {
  background-color: #b2d8b2;
}

/* Message Styles */
.message {
  padding: 10px 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-size: 0.9rem;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* List View Container */
.list-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  width: 100%;
}

.month-header {
  margin: 20px 0 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
  color: #333;
}

/* Date List Styles */
.agenda-layout {
  display: flex;
  gap: 20px;
}

.date-list-container {
  width: 300px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  flex-shrink: 0;
  max-height: 600px;
  overflow-y: auto;
  z-index: 10;
}

.agenda-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.agenda-content {
  width: 100%;
  max-width: 1200px; /* Increased from 1000px to make it wider */
  margin: 0 auto;
}

.date-list-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.date-list-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.date-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.date-item {
  margin-bottom: 10px;
  border-radius: 5px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.date-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.date-item.current-month {
  border-left: 3px solid #6b8e23;
}

.date-item.active {
  background-color: rgba(194, 216, 194, 0.3);
  border-left: 3px solid #6b8e23;
}

.date-link {
  display: flex;
  align-items: center;
  padding: 10px;
  text-decoration: none;
  color: #333;
  background-color: #f9f9f9;
}

.date-item-day {
  font-size: 1.5rem;
  font-weight: bold;
  color: #6b8e23;
  width: 40px;
  text-align: center;
}

.date-item-details {
  flex: 1;
  padding: 0 10px;
}

.date-item-month {
  font-size: 0.9rem;
  color: #666;
}

.date-item-weekday {
  font-size: 0.8rem;
  color: #999;
}

.date-item-count {
  font-size: 0.8rem;
  color: #333;
  background-color: #c2d8c2;
  padding: 2px 6px;
  border-radius: 10px;
}

.empty-dates {
  text-align: center;
  padding: 20px 0;
  color: #999;
}

/* Category selection in agenda modal */
.category-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.category-option {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.category-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: #6b8e23;
}

.category-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  text-decoration: none;
  color: #333;
}

.category-link i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #6b8e23;
}

.category-link h3 {
  margin: 10px 0;
  font-size: 1.2rem;
}

.category-link p {
  text-align: center;
  font-size: 0.9rem;
  color: #666;
}




