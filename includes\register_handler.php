<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Include user functions
require_once 'user_functions.php';

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'redirect' => ''
];

// Log the request method and POST data for debugging
error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
error_log("POST data: " . print_r($_POST, true));

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
    $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_SPECIAL_CHARS);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $password = $_POST['password']; // No sanitization for password
    $confirmPassword = $_POST['confirm_password']; // No sanitization for password
    // For debugging
    error_log("Terms value: " . (isset($_POST['terms']) ? $_POST['terms'] : 'not set'));

    // Skip terms validation for now to debug other issues
    $terms = true; // Force terms to be true for testing

    // Validate name
    if (empty($name)) {
        $response['message'] = 'Please enter your name.';
        echo json_encode($response);
        exit;
    }

    // Validate username
    if (empty($username)) {
        $response['message'] = 'Please choose a username.';
        echo json_encode($response);
        exit;
    }

    if (strlen($username) < 3) {
        $response['message'] = 'Username must be at least 3 characters long.';
        echo json_encode($response);
        exit;
    }

    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address.';
        echo json_encode($response);
        exit;
    }

    // Validate password
    if (strlen($password) < 6) {
        $response['message'] = 'Password must be at least 6 characters long.';
        echo json_encode($response);
        exit;
    }

    // Validate password confirmation
    if ($password !== $confirmPassword) {
        $response['message'] = 'Passwords do not match.';
        echo json_encode($response);
        exit;
    }

    // Skip terms validation for now
    // if (!$terms) {
    //     $response['message'] = 'You must agree to the Terms of Service and Privacy Policy.';
    //     echo json_encode($response);
    //     exit;
    // }

    // Try direct database query instead of using the registerUser function
    global $connection;

    try {
        // First, check if the Users table exists and has the username column
        $checkTableSql = "SHOW TABLES LIKE 'Users'";
        $tableResult = $connection->query($checkTableSql);

        if (!$tableResult || $tableResult->num_rows === 0) {
            // Users table doesn't exist, create it
            error_log("Users table doesn't exist. Creating it...");

            $createTableSql = "CREATE TABLE Users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('user', 'subscriber', 'admin') DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";

            if (!$connection->query($createTableSql)) {
                error_log("Failed to create Users table: " . $connection->error);
                $response['message'] = 'Database setup error. Please contact support.';
                echo json_encode($response);
                exit;
            }

            error_log("Users table created successfully.");
        } else {
            // Check if username column exists
            $checkColumnSql = "SHOW COLUMNS FROM Users LIKE 'username'";
            $columnResult = $connection->query($checkColumnSql);

            if (!$columnResult || $columnResult->num_rows === 0) {
                // Username column doesn't exist, add it
                error_log("Username column doesn't exist. Adding it...");

                $alterTableSql = "ALTER TABLE Users ADD COLUMN username VARCHAR(50) UNIQUE AFTER name";

                if (!$connection->query($alterTableSql)) {
                    error_log("Failed to add username column: " . $connection->error);
                    $response['message'] = 'Database update error. Please contact support.';
                    echo json_encode($response);
                    exit;
                }

                error_log("Username column added successfully.");

                // Update existing users to have a username based on their email
                $updateSql = "UPDATE Users SET username = SUBSTRING_INDEX(email, '@', 1) WHERE username IS NULL";
                if (!$connection->query($updateSql)) {
                    error_log("Failed to update existing users: " . $connection->error);
                    // Continue anyway, this is not critical
                }
            }
        }

        // Check if email already exists
        $checkEmailSql = "SELECT id FROM Users WHERE email = ?";
        $checkEmailStmt = $connection->prepare($checkEmailSql);

        if (!$checkEmailStmt) {
            error_log("Prepare failed (check email): (" . $connection->errno . ") " . $connection->error);
            $response['message'] = 'Database error. Please try again later.';
            echo json_encode($response);
            exit;
        }

        $checkEmailStmt->bind_param("s", $email);
        $checkEmailStmt->execute();
        $checkEmailResult = $checkEmailStmt->get_result();

        if ($checkEmailResult && $checkEmailResult->num_rows > 0) {
            // Email already exists
            $response['message'] = 'This email is already registered. Please use a different email or try logging in.';
            echo json_encode($response);
            exit;
        }

        // Check if username already exists
        $checkUsernameSql = "SELECT id FROM Users WHERE username = ?";
        $checkUsernameStmt = $connection->prepare($checkUsernameSql);

        if (!$checkUsernameStmt) {
            error_log("Prepare failed (check username): (" . $connection->errno . ") " . $connection->error);
            $response['message'] = 'Database error. Please try again later.';
            echo json_encode($response);
            exit;
        }

        $checkUsernameStmt->bind_param("s", $username);
        $checkUsernameStmt->execute();
        $checkUsernameResult = $checkUsernameStmt->get_result();

        if ($checkUsernameResult && $checkUsernameResult->num_rows > 0) {
            // Username already exists
            $response['message'] = 'This username is already taken. Please choose a different username.';
            echo json_encode($response);
            exit;
        }

        // Use a simpler approach - just insert without the role column
        // This avoids issues with parameter binding
        $sql = "INSERT INTO Users (name, username, email, password) VALUES (?, ?, ?, ?)";

        // Log the SQL for debugging
        error_log("Using SQL: " . $sql);

        $stmt = $connection->prepare($sql);

        if (!$stmt) {
            error_log("Prepare failed (insert): (" . $connection->errno . ") " . $connection->error);
            $response['message'] = 'Database error. Please try again later.';
            echo json_encode($response);
            exit;
        }

        // Hash the password for security
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Simple parameter binding - always 4 parameters
        try {
            $stmt->bind_param("ssss", $name, $username, $email, $hashedPassword);
            error_log("Parameters bound successfully");
        } catch (Exception $e) {
            error_log("Exception during bind_param: " . $e->getMessage());
            $response['message'] = 'Error during account creation: ' . $e->getMessage();
            echo json_encode($response);
            exit;
        }

        error_log("Attempting to insert new user: $name, $username, $email");

        try {
            if ($stmt->execute()) {
                $userId = $connection->insert_id;

                // Set session variables
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_name'] = $name;
                $_SESSION['user_username'] = $username;
                $_SESSION['user_email'] = $email;
                $_SESSION['logged_in'] = true;

                // Only set a remember me token if the checkbox was checked
                $remember = isset($_POST['remember_me']) && $_POST['remember_me'] === 'on';
                error_log("Remember me for registration: " . ($remember ? 'true' : 'false'));

                if ($remember) {
                    $token = bin2hex(random_bytes(32));
                    $expiry = date('Y-m-d H:i:s', strtotime('+30 days'));
                    storeRememberToken($userId, $token, $expiry);
                    setcookie('remember_token', $token, time() + (86400 * 30), '/', '', false, true);
                    error_log("Set remember token for new registration: $token");
                }

                $response['success'] = true;
                $response['message'] = 'Registration successful! Welcome to PregnancyCare.';
                $response['redirect'] = '../index.php';
            } else {
                error_log("Execute failed: (" . $stmt->errno . ") " . $stmt->error);
                $response['message'] = 'Error creating account: ' . $stmt->error;
            }
        } catch (Exception $e) {
            error_log("Exception during execute: " . $e->getMessage());
            $response['message'] = 'Error during account creation: ' . $e->getMessage();
        }
    } catch (Exception $e) {
        error_log("Exception in registration: " . $e->getMessage());
        $response['message'] = 'An unexpected error occurred: ' . $e->getMessage();
    }
}

// Log the response for debugging
error_log("Response: " . print_r($response, true));

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
