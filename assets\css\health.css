.health-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.health-header h1 {
    margin: 0;
    color: #6b8e23;
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.content-wrapper {
    display: flex;
    gap: 30px;
}

.content-box {
    flex: 1;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.content-box h2 {
    color: #6b8e23;
    margin-bottom: 20px;
}

.symptom-card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.symptom-card h3 {
    color: #6b8e23;
    margin-top: 0;
    margin-bottom: 10px;
}

.symptom-card .date {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.symptom-card .severity {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.severity.mild {
    background-color: #e8f5e9;
    color: #2e8b57;
}

.severity.moderate {
    background-color: #fff3e0;
    color: #f57c00;
}

.severity.severe {
    background-color: #ffebee;
    color: #c62828;
}

@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
    }
    
    .content-box {
        width: 100%;
    }
}

/* Update any button styles in health.css */
.add-symptom-btn,
.record-btn,
.action-button {
    background-color: #b2d8b2;
    color: white;
    transition: all 0.3s ease;
}

.add-symptom-btn:hover,
.record-btn:hover,
.action-button:hover {
    background-color: #9ac89a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Keep headings with the original dark green */
.health-header h1,
.content-box h2,
.symptom-card h3 {
    color: #6b8e23;  /* Maintain dark green for text elements */
}
