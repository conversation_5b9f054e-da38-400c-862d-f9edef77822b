<?php

/**
 * Database connection script
 * This file establishes a connection to the MySQL database
 */

$servername = "localhost";
$username   = "root";
$password   = "admin"; 
$dbname     = "project";

try {
    $connection = new mysqli($servername, $username, $password);
    
    if ($connection->connect_error) {
        throw new Exception("Connection failed: " . $connection->connect_error);
    }
    
    if (!$connection->query("CREATE DATABASE IF NOT EXISTS $dbname")) {
        throw new Exception("Error creating database: " . $connection->error);
    }
    
    $connection->select_db($dbname);
    $connection->set_charset("utf8mb4");
} catch (Exception $e) {
    throw $e;
}
