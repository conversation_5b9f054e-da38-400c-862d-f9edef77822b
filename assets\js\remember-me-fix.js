/**
 * Fix for Remember Me functionality
 * This script fixes the Remember Me checkbox functionality for all login forms
 */
document.addEventListener('DOMContentLoaded', function() {
    // Fix for all Remember Me checkboxes
    const allRememberMeCheckboxes = document.querySelectorAll('input[type="checkbox"][name="remember_me"]');
    
    console.log('Found', allRememberMeCheckboxes.length, 'remember me checkboxes');
    
    allRememberMeCheckboxes.forEach(checkbox => {
        // Add event listener to handle the checkbox state
        checkbox.addEventListener('change', function() {
            console.log('Remember me checkbox changed:', this.checked);
            
            // Store the checkbox state in localStorage to persist it
            const formId = this.form ? this.form.id || this.form.action : 'unknown';
            localStorage.setItem('remember_me_' + formId, this.checked);
        });
        
        // Restore checkbox state from localStorage if available
        const formId = checkbox.form ? checkbox.form.id || checkbox.form.action : 'unknown';
        const savedState = localStorage.getItem('remember_me_' + formId);
        if (savedState === 'true') {
            checkbox.checked = true;
            console.log('Restored remember me state to checked for form:', formId);
        }
    });
    
    // Handle all forms with remember_me checkbox
    const allForms = document.querySelectorAll('form');
    allForms.forEach(form => {
        // Check if the form has a remember_me checkbox
        const rememberCheckbox = form.querySelector('input[name="remember_me"]');
        if (rememberCheckbox) {
            console.log('Found form with remember_me checkbox:', form.id || form.action);
            
            // Mark the form as having a remember_me checkbox
            form.setAttribute('data-has-remember-me', 'true');
            
            // Add submit event listener if the form doesn't already have a data-has-submit-handler attribute
            if (!form.hasAttribute('data-has-submit-handler')) {
                form.addEventListener('submit', function(e) {
                    // Get form data
                    const formData = new FormData(form);
                    
                    // Special handling for remember_me checkbox
                    if (rememberCheckbox.checked) {
                        formData.set('remember_me', 'on');
                        console.log('Remember me is checked in form:', form.id || form.action);
                    }
                    
                    // Log the form data for debugging
                    console.log('Form data:');
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ': ' + (pair[0] === 'password' ? '********' : pair[1]));
                    }
                });
            }
        }
    });
});
