<?php
// Start session
session_start();

// Include user functions
require_once 'user_functions.php';

// Initialize response array
$response = [
    'success'  => false,
    'message'  => '',
    'redirect' => '',
];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $login_id = filter_input(INPUT_POST, 'login_id', FILTER_SANITIZE_SPECIAL_CHARS);
    $password = $_POST['password']; // No sanitization for password

    // Handle remember me checkbox - it will be 'on' if checked
    $remember = isset($_POST['remember_me']) && $_POST['remember_me'] === 'on';

    // Debug remember me value
    error_log("Remember me POST value: " . (isset($_POST['remember_me']) ? $_POST['remember_me'] : 'not set'));
    error_log("Remember me boolean: " . ($remember ? 'true' : 'false'));

    // Additional debugging for all POST data
    error_log("All POST data: " . print_r($_POST, true));

    // Validate login ID (username or email)
    if (empty($login_id)) {
        $response['message'] = 'Please enter your username or email address.';
        echo json_encode($response);
        exit;
    }

    // Validate password
    if (empty($password)) {
        $response['message'] = 'Please enter your password.';
        echo json_encode($response);
        exit;
    }

    // Check if the username column exists in the Users table
    $checkColumnSql = "SHOW COLUMNS FROM Users LIKE 'username'";
    $columnResult   = $connection->query($checkColumnSql);

    if (! $columnResult || $columnResult->num_rows === 0) {

        $alterTableSql = "ALTER TABLE Users ADD COLUMN username VARCHAR(50) UNIQUE AFTER name";

        if ($connection->query($alterTableSql) === true) {
            error_log("Username column added successfully.");

            // Update existing users to have a username based on their email
            $updateSql = "UPDATE Users SET username = SUBSTRING_INDEX(email, '@', 1) WHERE username IS NULL";
            if (! $connection->query($updateSql)) {
                error_log("Failed to update existing users: " . $connection->error);
                // Continue anyway, this is not critical
            }
        } else {
            error_log("Failed to add username column: " . $connection->error);
            $response['message'] = 'Database update error. Please try again later.';
            echo json_encode($response);
            exit;
        }
    }

    // Authenticate user
    $user = authenticateUser($login_id, $password);

    if ($user) {
        // Set session variables
        $_SESSION['user_id']       = $user['id'];
        $_SESSION['user_name']     = $user['name'];
        $_SESSION['user_username'] = isset($user['username']) ? $user['username'] : '';
        $_SESSION['user_email']    = $user['email'];
        $_SESSION['user_role']     = isset($user['role']) ? $user['role'] : 'user';
        $_SESSION['logged_in']     = true;

        // Handle "Remember Me" functionality
        if ($remember) {
            // Generate a unique token
            $token = bin2hex(random_bytes(32));

            // Store the token in the database
            $expiry = date('Y-m-d H:i:s', strtotime('+30 days'));
            storeRememberToken($user['id'], $token, $expiry);

            // Set a cookie with the token (30 days expiry)
            setcookie('remember_token', $token, time() + (86400 * 30), '/', '', false, true);
        }

        $response['success'] = true;
        $response['message'] = 'Login successful!';

        // Redirect to the page the user was trying to access, if set
        if (isset($_SESSION['redirect_after_login'])) {
            $response['redirect'] = $_SESSION['redirect_after_login'];
            unset($_SESSION['redirect_after_login']); // Clear the redirect URL
        } else {
            $response['redirect'] = '../index.php';
        }
    } else {
        $response['message'] = 'Invalid username/email or password.';
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
