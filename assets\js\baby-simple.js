/**
 * Simple JavaScript for the baby page
 * This file contains basic functionality for the baby page without any complex code
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('baby-simple.js loaded');

    // Function to show the baby form
    window.showBabyForm = function() {
        console.log('Showing baby form');
        var form = document.querySelector('.baby-form-container');

        if (form) {
            // Show the form
            form.style.display = 'block';

            // No need to scroll the page - the form is sized to fit within the viewport

            // Focus on the first input field
            setTimeout(function() {
                var firstInput = form.querySelector('input[type="text"]');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 100);
        }

        // We no longer change the button text - the Add New Baby button stays the same
        // The form has its own close button

        return false;
    };

    // Function to hide the baby form
    window.hideBabyForm = function() {
        console.log('Hiding baby form');
        var form = document.querySelector('.baby-form-container');

        if (form) {
            // Hide the form
            form.style.display = 'none';
        }

        // No need to change the button text back - it stays the same

        return false;
    };

    // Function to switch to grid view
    window.showGridView = function() {
        console.log('Switching to grid view');
        var grid = document.querySelector('.babies-grid');
        if (grid) {
            grid.classList.remove('list-view');
            grid.classList.add('grid-view');
        }

        // Update active button
        var gridBtn = document.querySelector('.grid-view-btn');
        var listBtn = document.querySelector('.list-view-btn');

        if (gridBtn) {
            gridBtn.classList.add('active');
        }

        if (listBtn) {
            listBtn.classList.remove('active');
        }

        // Save preference
        localStorage.setItem('babyViewPreference', 'grid');

        return false;
    };

    // Function to switch to list view
    window.showListView = function() {
        console.log('Switching to list view');
        var grid = document.querySelector('.babies-grid');
        if (grid) {
            grid.classList.remove('grid-view');
            grid.classList.add('list-view');
        }

        // Update active button
        var gridBtn = document.querySelector('.grid-view-btn');
        var listBtn = document.querySelector('.list-view-btn');

        if (gridBtn) {
            gridBtn.classList.remove('active');
        }

        if (listBtn) {
            listBtn.classList.add('active');
        }

        // Save preference
        localStorage.setItem('babyViewPreference', 'list');

        return false;
    };

    // Initialize the page
    function initPage() {
        // Add click handlers to buttons - the Add New Baby button always shows the form
        var addBabyBtn = document.querySelector('.add-baby-btn');
        if (addBabyBtn) {
            // Make sure it always shows the form
            addBabyBtn.setAttribute('onclick', 'showBabyForm(); return false;');
        }

        var closeFormBtn = document.querySelector('.close-form-btn');
        if (closeFormBtn) {
            closeFormBtn.setAttribute('onclick', 'hideBabyForm(); return false;');
        }

        var cancelBtn = document.querySelector('#cancel-add-baby');
        if (cancelBtn) {
            cancelBtn.setAttribute('onclick', 'hideBabyForm(); return false;');
        }

        var gridViewBtn = document.querySelector('.grid-view-btn');
        if (gridViewBtn) {
            gridViewBtn.setAttribute('onclick', 'showGridView(); return false;');
        }

        var listViewBtn = document.querySelector('.list-view-btn');
        if (listViewBtn) {
            listViewBtn.setAttribute('onclick', 'showListView(); return false;');
        }

        // No overlay click handler needed

        // Load saved view preference
        var savedPreference = localStorage.getItem('babyViewPreference');
        if (savedPreference === 'list') {
            showListView();
        } else {
            showGridView();
        }
    }

    // Initialize the page
    initPage();
});
