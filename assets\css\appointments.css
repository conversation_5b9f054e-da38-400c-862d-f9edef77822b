.appointment-card {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.appointment-card h3 {
    margin-top: 0;
    color: #6b8e23;
}

.appointment-date {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.appointment-actions {
    margin-top: 15px;
    text-align: right;
}

.appointment-actions a,
.appointment-actions button {
    display: inline-block;
    margin-left: 10px;
}

.message {
    padding: 10px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.message.success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}

.message.error {
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #c62828;
}