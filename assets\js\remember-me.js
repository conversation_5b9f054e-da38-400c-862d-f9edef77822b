/**
 * Remember Me Functionality
 *
 * This script handles the "Remember Me" checkbox functionality for all forms.
 * It ensures that:
 * 1. The checkbox state is saved to localStorage when changed
 * 2. The checkbox state is restored from localStorage when the page loads
 * 3. The checkbox value is properly included in form submissions
 * 4. The checkbox is properly clickable and shows visual feedback
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find all Remember Me checkboxes
    const allRememberMeCheckboxes = document.querySelectorAll('input[type="checkbox"][name="remember_me"]');

    console.log('Found', allRememberMeCheckboxes.length, 'remember me checkboxes');

    // Process each checkbox
    allRememberMeCheckboxes.forEach(checkbox => {
        // Get the form ID or a unique identifier
        const form = checkbox.form;
        const formId = form ? (form.id || form.action) : 'unknown';

        // Restore checkbox state from localStorage if available
        const savedState = localStorage.getItem('remember_me_' + formId);
        if (savedState === 'true') {
            checkbox.checked = true;
            console.log('Restored remember me state to checked for form:', formId);
        }

        // Save checkbox state when it changes
        checkbox.addEventListener('change', function() {
            console.log('Remember me checkbox changed:', this.checked);
            localStorage.setItem('remember_me_' + formId, this.checked);
        });

        // Make the entire custom checkbox container clickable
        const customCheckbox = checkbox.closest('.custom-checkbox');
        if (customCheckbox) {
            customCheckbox.addEventListener('click', function(e) {
                // Don't toggle if the click was on the checkbox itself
                if (e.target === checkbox) {
                    return;
                }

                // Toggle the checkbox state
                checkbox.checked = !checkbox.checked;

                // Trigger the change event manually
                const changeEvent = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(changeEvent);

                console.log('Custom checkbox clicked, new state:', checkbox.checked);
            });
        }

        // Make the label clickable too (in case it's not already)
        const label = document.querySelector(`label[for="${checkbox.id}"]`);
        if (label) {
            label.addEventListener('click', function(e) {
                // Prevent the default behavior to avoid double-toggling
                e.preventDefault();

                // Toggle the checkbox state
                checkbox.checked = !checkbox.checked;

                // Trigger the change event manually
                const changeEvent = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(changeEvent);

                console.log('Label clicked, new state:', checkbox.checked);
            });
        }

        // Ensure the form includes the checkbox value when submitted
        if (form && !form.hasAttribute('data-remember-me-handler')) {
            form.setAttribute('data-remember-me-handler', 'true');

            // Add a submit event listener to ensure the checkbox value is included
            form.addEventListener('submit', function(e) {
                // Don't prevent default here, as other handlers might need to do that

                // Log the checkbox state
                console.log('Form submitted with remember me state:', checkbox.checked);

                // The FormData object will automatically include the checkbox if it's checked
                // But we can explicitly set it for clarity
                if (this.method.toLowerCase() === 'post') {
                    // Only modify if it's a POST form
                    const formData = new FormData(this);
                    if (checkbox.checked) {
                        formData.set('remember_me', 'on');
                    }

                    // Log all form data for debugging
                    console.log('Form data:');
                    for (let pair of formData.entries()) {
                        console.log(pair[0] + ': ' + (pair[0] === 'password' ? '********' : pair[1]));
                    }
                }
            });
        }
    });
});
