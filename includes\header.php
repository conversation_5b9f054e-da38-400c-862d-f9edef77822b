<?php

/**
 * Header template for PregnancyCare
 * This file contains the header that is used across all pages of the website
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include database connection and user functions
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/user_functions.php';

// Check if user is logged in
$isLoggedIn = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;

// If not logged in, check for remember me cookie
if (!$isLoggedIn && isset($_COOKIE['remember_token'])) {
    $token = $_COOKIE['remember_token'];
    $user = getUserByRememberToken($token);

    if ($user) {
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_username'] = isset($user['username']) ? $user['username'] : '';
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = isset($user['role']) ? $user['role'] : 'user';
        $_SESSION['logged_in'] = true;

        // Refresh the cookie
        setcookie('remember_token', $token, time() + (86400 * 30), '/', '', false, true);

        $isLoggedIn = true;
    }
}

$userName = $isLoggedIn ? $_SESSION['user_name'] : '';

// Determine if we're in the root directory or a subdirectory
$scriptPath     = $_SERVER['SCRIPT_FILENAME'];
$isSubdirectory = (strpos($scriptPath, 'pages') !== false);
$rootPath       = $isSubdirectory ? '../' : '';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>PregnancyCare</title>
    <!-- Link to Stylesheet -->
    <link rel="stylesheet" href="<?php echo $rootPath; ?>assets/css/style.css">
    <!-- Add these after your main style.css link -->
    <?php if ($activePage === 'appointments'): ?>
        <link rel="stylesheet" href="<?php echo $rootPath; ?>assets/css/appointments.css">
    <?php endif; ?>
    <?php if ($activePage === 'baby'): ?>
        <link rel="stylesheet" href="<?php echo $rootPath; ?>assets/css/baby.css">
    <?php endif; ?>
    <?php if ($activePage === 'health'): ?>
        <link rel="stylesheet" href="<?php echo $rootPath; ?>assets/css/health.css">
    <?php endif; ?>
    <?php if ($activePage === 'agenda'): ?>
        <link rel="stylesheet" href="<?php echo $rootPath; ?>assets/css/agenda.css">
    <?php endif; ?>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manjari:wght@100;400;700&display=swap" rel="stylesheet">
    <!-- Dropdown Menu Enhancement -->
    <link rel="stylesheet" href="<?php echo $rootPath; ?>assets/css/dropdown-fix.css">
    <!-- Remember Me Fix -->
    <script src="<?php echo $rootPath; ?>assets/js/remember-me-fix.js"></script>
    <script src="<?php echo $rootPath; ?>assets/js/remember-me.js"></script>
    <!-- Favicon -->
    <link rel="icon" href="/assets/icons/logo.png" type="image/png">
    <?php if (isset($additionalHeadContent)) {
        echo $additionalHeadContent;
    }
    ?>
</head>

<body>
    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Welcome Back</h2>
            <div id="login-message" class="message" style="display: none;"></div>
            <form id="login-form" method="post" action="<?php echo $rootPath; ?>includes/login_handler.php">
                <div class="form-group">
                    <label for="login_id">Username or Email:</label>
                    <input type="text" id="login_id" name="login_id" required placeholder="Enter your username or email address">
                </div>
                <div class="form-group">
                    <label for="password">Your Password:</label>
                    <input type="password" id="password" name="password" required placeholder="Enter your password">
                </div>

                <div class="remember-me-container checkbox-container">
                    <label for="remember_me">Remember me</label>
                    <div class="custom-checkbox">
                        <input type="checkbox" id="remember_me" name="remember_me">
                        <span class="checkmark"></span>
                    </div>
                </div>

                <script>
                    // Add focus/blur event listeners to the remember me checkbox
                    document.addEventListener('DOMContentLoaded', function() {
                        const rememberCheckbox = document.getElementById('remember_me');
                        const rememberLabel = document.querySelector('.remember-me-container label');

                        if (rememberCheckbox) {
                            // Restore checkbox state from localStorage if available
                            const savedState = localStorage.getItem('remember_me_login-form');
                            if (savedState === 'true') {
                                rememberCheckbox.checked = true;
                                console.log('Restored remember me state to checked for login form');
                            }

                            // Save checkbox state when it changes
                            rememberCheckbox.addEventListener('change', function() {
                                console.log('Remember me checkbox changed:', this.checked);
                                localStorage.setItem('remember_me_login-form', this.checked);
                            });

                            // Focus/blur effects
                            rememberCheckbox.addEventListener('focus', function() {
                                rememberLabel.classList.add('focused');
                            });

                            rememberCheckbox.addEventListener('blur', function() {
                                rememberLabel.classList.remove('focused');
                            });
                        }
                    });
                </script>

                <button type="submit" class="login-submit-btn">Sign In</button>
                <div class="form-footer">
                    <a href="#forgot-password">Forgot Your Password?</a>
                    <a href="#" id="register-link">Create an Account</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Registration Modal -->
    <div id="register-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Join Our Community</h2>
            <div id="register-message" class="message" style="display: none;"></div>
            <form id="register-form" method="post" action="<?php echo $rootPath; ?>includes/register_handler.php" novalidate>
                <div class="form-group">
                    <label for="reg-name">Your Name:</label>
                    <input type="text" id="reg-name" name="name" required placeholder="Enter your full name">
                </div>
                <div class="form-group">
                    <label for="reg-username">Choose a Username:</label>
                    <input type="text" id="reg-username" name="username" required placeholder="Choose a unique username" minlength="3" maxlength="50">
                    <small class="form-text">Username must be at least 3 characters long</small>
                </div>
                <div class="form-group">
                    <label for="reg-email">Your Email:</label>
                    <input type="email" id="reg-email" name="email" required placeholder="Enter your email address">
                </div>
                <div class="form-group">
                    <label for="reg-password">Create a Password:</label>
                    <input type="password" id="reg-password" name="password" required placeholder="Choose a secure password" minlength="6">
                    <small class="form-text">For your security, use at least 6 characters</small>
                </div>
                <div class="form-group">
                    <label for="reg-confirm-password">Confirm Your Password:</label>
                    <input type="password" id="reg-confirm-password" name="confirm_password" required placeholder="Type your password again">
                </div>
                <div class="terms-container">
                    <input type="checkbox" id="terms" name="terms" required>
                    <label for="terms">I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a> of PregnancyCare</label>
                </div>

                <div class="remember-me-container checkbox-container">
                    <label for="reg_remember_me">Remember me</label>
                    <div class="custom-checkbox">
                        <input type="checkbox" id="reg_remember_me" name="remember_me">
                        <span class="checkmark"></span>
                    </div>
                </div>
                <button type="submit" class="register-submit-btn">Join PregnancyCare</button>
                <div class="form-footer">
                    <p>Already a member? <a href="#" id="login-link-from-register">Sign in here</a></p>
                </div>
            </form>
        </div>
    </div>

    <div class="wrapper">
        <nav>
            <div class="nav-container">
                <div class="logo-container">
                    <a href="<?php echo $rootPath; ?>index.php" class="logo">PregnancyCare</a>
                </div>
                
                <!-- Mobile menu button -->
                <button id="mobile-menu-btn" class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
                
                <!-- Navigation links -->
                <div id="nav-menu" class="nav-menu">
                    <a href="<?php echo $rootPath; ?>index.php#pregnancy" <?php echo (isset($activePage) && $activePage === 'pregnancy') ? 'class="active"' : ''; ?>>Pregnancy</a>
                    <a href="<?php echo $rootPath; ?>index.php#community" <?php echo (isset($activePage) && $activePage === 'community') ? 'class="active"' : ''; ?>>Community</a>
                    <a href="<?php echo $rootPath; ?>pages/agenda.php" <?php echo (isset($activePage) && $activePage === 'agenda') ? 'class="active"' : ''; ?>>Agenda</a>
                    <a href="<?php echo $rootPath; ?>pages/health.php" <?php echo (isset($activePage) && $activePage === 'health') ? 'class="active"' : ''; ?>>Health</a>
                    <a href="<?php echo $rootPath; ?>index.php#family" <?php echo (isset($activePage) && $activePage === 'family') ? 'class="active"' : ''; ?>>Family</a>
                    <a href="<?php echo $rootPath; ?>pages/baby.php" <?php echo (isset($activePage) && $activePage === 'baby') ? 'class="active"' : ''; ?>>Baby</a>
                    <a href="<?php echo $rootPath; ?>pages/appointments.php" <?php echo (isset($activePage) && $activePage === 'appointments') ? 'class="active"' : ''; ?>>Appointments</a>
                </div>
                
                <!-- Login container -->
                <div class="login-container">
                    <?php if ($isLoggedIn): ?>
                        <div class="user-menu">
                            <a href="#" class="user-menu-btn">
                                <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($userName); ?>
                            </a>
                            <div class="user-dropdown">
                                <a href="<?php echo $rootPath; ?>pages/profile.php"><i class="fas fa-user"></i> Profile</a>
                                <?php if ($isAdmin): ?>
                                    <a href="<?php echo $rootPath; ?>pages/admin.php"><i class="fas fa-users-cog"></i> Admin Panel</a>
                                <?php endif; ?>
                                <a href="<?php echo $rootPath; ?>includes/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="#" class="login-trigger login-btn">Sign In</a>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
    </div>
    <!-- Add this simple mobile menu script right after the nav -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Simple mobile menu toggle
        const menuBtn = document.getElementById('mobile-menu-btn');
        const navMenu = document.getElementById('nav-menu');
        
        if (menuBtn && navMenu) {
            menuBtn.addEventListener('click', function() {
                navMenu.classList.toggle('show');
                menuBtn.classList.toggle('active');
                
                // Toggle icon between bars and times
                const icon = menuBtn.querySelector('i');
                if (icon) {
                    if (icon.classList.contains('fa-bars')) {
                        icon.classList.remove('fa-bars');
                        icon.classList.add('fa-times');
                    } else {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                }
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                if (navMenu.classList.contains('show') && 
                    !navMenu.contains(event.target) && 
                    !menuBtn.contains(event.target)) {
                    navMenu.classList.remove('show');
                    menuBtn.classList.remove('active');
                    
                    // Reset icon
                    const icon = menuBtn.querySelector('i');
                    if (icon) {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                }
            });
        }
    });
    </script>
</body>

</html>
