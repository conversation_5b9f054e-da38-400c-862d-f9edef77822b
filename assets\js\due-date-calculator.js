/**
 * Due Date Calculator functionality
 * Handles the calculation of estimated due dates based on last menstrual period
 * and cycle length.
 */
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.calculator-form');
    const resultSpan = document.getElementById('due-date');

    if (!form || !resultSpan) {
        console.warn('Due date calculator elements not found');
        return;
    }

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const lastPeriodInput = document.getElementById('last-period');
        const cycleInput = document.getElementById('cycle-length');
        
        if (!lastPeriodInput || !cycleInput) {
            console.error('Required form inputs not found');
            return;
        }

        const lastPeriodDate = new Date(lastPeriodInput.value);
        const cycleLength = parseInt(cycleInput.value);
        
        // Validate date is not in the future
        if (lastPeriodDate > new Date()) {
            alert('Please select a date in the past');
            return;
        }

        // Calculate ovulation date (14 days before next period)
        const ovulationDate = new Date(lastPeriodDate);
        ovulationDate.setDate(lastPeriodDate.getDate() + (cycleLength - 14));

        // Calculate due date (266 days from ovulation)
        const dueDate = new Date(ovulationDate);
        dueDate.setDate(ovulationDate.getDate() + 266);

        // Format the date
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        resultSpan.textContent = dueDate.toLocaleDateString('en-US', options);
    });

    // Add max date validation for the date input
    const lastPeriodInput = document.getElementById('last-period');
    if (lastPeriodInput) {
        const today = new Date().toISOString().split('T')[0];
        lastPeriodInput.setAttribute('max', today);
    }
});