<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Setup</h2>";

// Database connection parameters
$servername = "localhost";
$username   = "root";
$password   = "admin";
$dbname     = "project";

try {
    // Connect to MySQL server (without specifying database)
    $connection = new mysqli($servername, $username, $password);

    if ($connection->connect_error) {
        die("Connection failed: " . $connection->connect_error);
    }

    echo "✓ Connected to MySQL server<br>";

    // Create database if it doesn't exist
    $sql = "CREATE DATABASE IF NOT EXISTS $dbname";
    if ($connection->query($sql) === true) {
        echo "✓ Database '$dbname' created or already exists<br>";
    } else {
        echo "Error creating database: " . $connection->error . "<br>";
    }

    // Select the database
    $connection->select_db($dbname);
    echo "✓ Selected database '$dbname'<br>";

    // Set character set
    $connection->set_charset("utf8mb4");

    // Create tables
    $tables = [
        "Users"        => "CREATE TABLE IF NOT EXISTS Users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100),
            username VARCHAR(50) UNIQUE,
            email VARCHAR(100) UNIQUE,
            password VARCHAR(255),
            role VARCHAR(20) DEFAULT 'user',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",

        "Pregnancy"    => "CREATE TABLE IF NOT EXISTS Pregnancy (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            title VARCHAR(255),
            start_date DATE,
            due_date DATE,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES Users(id)
        )",

        "BabyGender"   => "CREATE TABLE IF NOT EXISTS BabyGender (
            id INT AUTO_INCREMENT PRIMARY KEY,
            gender_name VARCHAR(20) UNIQUE,
            description TEXT
        )",

        "BabyNames"    => "CREATE TABLE IF NOT EXISTS BabyNames (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100),
            gender VARCHAR(10),
            origin VARCHAR(100),
            meaning TEXT
        )",

        "Baby"         => "CREATE TABLE IF NOT EXISTS Baby (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            name VARCHAR(100),
            gender_id INT,
            birth_date DATE,
            weight DECIMAL(5,2),
            height DECIMAL(5,2),
            pregnancy_id INT,
            baby_name_id INT,
            FOREIGN KEY (user_id) REFERENCES Users(id),
            FOREIGN KEY (gender_id) REFERENCES BabyGender(id),
            FOREIGN KEY (pregnancy_id) REFERENCES Pregnancy(id) ON DELETE SET NULL,
            FOREIGN KEY (baby_name_id) REFERENCES BabyNames(id) ON DELETE SET NULL
        )",

        "Community"    => "CREATE TABLE IF NOT EXISTS Community (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            title VARCHAR(255),
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE SET NULL
        )",

        "Agenda"       => "CREATE TABLE IF NOT EXISTS Agenda (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            baby_id INT,
            title VARCHAR(255),
            description TEXT,
            date DATE,
            time TIME,
            FOREIGN KEY (user_id) REFERENCES Users(id),
            FOREIGN KEY (baby_id) REFERENCES Baby(id) ON DELETE SET NULL
        )",

        "Health"       => "CREATE TABLE IF NOT EXISTS Health (
            id INT AUTO_INCREMENT PRIMARY KEY,
            symptom_name VARCHAR(100) UNIQUE,
            tip TEXT
        )",

        "Symptoms"     => "CREATE TABLE IF NOT EXISTS Symptoms (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            health_id INT,
            agenda_id INT,
            severity VARCHAR(50),
            date_logged DATE,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES Users(id),
            FOREIGN KEY (health_id) REFERENCES Health(id),
            FOREIGN KEY (agenda_id) REFERENCES Agenda(id) ON DELETE SET NULL
        )",

        "Doctors"      => "CREATE TABLE IF NOT EXISTS Doctors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            specialty VARCHAR(100),
            phone VARCHAR(20),
            email VARCHAR(100),
            notes TEXT
        )",

        "Locations"    => "CREATE TABLE IF NOT EXISTS Locations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            address VARCHAR(255),
            city VARCHAR(100),
            postal_code VARCHAR(20),
            phone VARCHAR(20),
            notes TEXT
        )",

        "Appointments" => "CREATE TABLE IF NOT EXISTS Appointments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            doctor_id INT,
            location_id INT,
            agenda_id INT,
            date DATE,
            time TIME,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES Users(id),
            FOREIGN KEY (doctor_id) REFERENCES Doctors(id) ON DELETE SET NULL,
            FOREIGN KEY (location_id) REFERENCES Locations(id) ON DELETE SET NULL,
            FOREIGN KEY (agenda_id) REFERENCES Agenda(id) ON DELETE CASCADE
        )",
    ];

    foreach ($tables as $tableName => $sql) {
        if ($connection->query($sql) === true) {
            echo "✓ Table '$tableName' created or already exists<br>";
        } else {
            echo "✗ Error creating table '$tableName': " . $connection->error . "<br>";
        }
    }

    echo "<br><h3>Inserting Default Data</h3>";

    // Insert default BabyGender values
    $genderCheck = $connection->query("SELECT COUNT(*) as count FROM BabyGender");
    if ($genderCheck && $genderCheck->fetch_assoc()['count'] == 0) {
        $genderSql = "INSERT INTO BabyGender (gender_name, description) VALUES
            ('Boy', 'Male gender'),
            ('Girl', 'Female gender'),
            ('Neutral', 'Neutral or unspecified gender')";

        if ($connection->query($genderSql) === true) {
            echo "✓ Default baby genders inserted<br>";
        } else {
            echo "✗ Error inserting baby genders: " . $connection->error . "<br>";
        }
    } else {
        echo "✓ Baby genders already exist<br>";
    }

    echo "<br><h3>Database Setup Complete!</h3>";
    echo "<p><a href='index.php'>Go to Home Page</a></p>";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}

$connection->close();
