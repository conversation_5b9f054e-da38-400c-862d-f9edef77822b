/**
 * Enhanced dropdown menu behavior
 * This script improves the user experience with the dropdown menu by:
 * 1. Adding a significant delay before closing the dropdown
 * 2. Allowing users to move the mouse from the button to the dropdown without it closing
 * 3. Supporting both click and hover interactions
 * 4. Adding keyboard accessibility
 * 5. Making the dropdown behavior consistent across all pages
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add a class to indicate JavaScript is enabled
    document.body.classList.add('js-enabled');

    // Get the user menu elements
    const userMenu = document.querySelector('.user-menu');
    const userDropdown = document.querySelector('.user-dropdown');
    const userMenuBtn = document.querySelector('.user-menu-btn');

    // Exit if elements don't exist
    if (!userMenu || !userDropdown || !userMenuBtn) {
        return;
    }

    // Variables for tracking state
    let timeoutId;
    let isDropdownOpen = false;

    // Function to show the dropdown
    function showDropdown() {
        clearTimeout(timeoutId);
        userDropdown.classList.add('show');
        userMenuBtn.setAttribute('aria-expanded', 'true');
        isDropdownOpen = true;
    }

    // Function to hide the dropdown with a delay
    function hideDropdownWithDelay() {
        timeoutId = setTimeout(function() {
            userDropdown.classList.remove('show');
            userMenuBtn.setAttribute('aria-expanded', 'false');
            isDropdownOpen = false;
        }, 750); // 0.75 second delay before hiding
    }

    // Function to hide the dropdown immediately
    function hideDropdown() {
        userDropdown.classList.remove('show');
        userMenuBtn.setAttribute('aria-expanded', 'false');
        isDropdownOpen = false;
    }

    // Toggle dropdown when clicking the user menu button
    userMenuBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation(); // Prevent event from bubbling up

        if (isDropdownOpen) {
            hideDropdown();
        } else {
            showDropdown();
        }
    });

    // Show dropdown when hovering over the user menu button
    userMenuBtn.addEventListener('mouseenter', function() {
        clearTimeout(timeoutId);
        showDropdown();
    });

    // Keep dropdown open when hovering over it
    userDropdown.addEventListener('mouseenter', function() {
        clearTimeout(timeoutId);
    });

    // Add a significant delay before hiding the dropdown when leaving the user menu
    userMenu.addEventListener('mouseleave', function() {
        hideDropdownWithDelay();
    });

    // Add a significant delay before hiding the dropdown when leaving the dropdown itself
    userDropdown.addEventListener('mouseleave', function() {
        hideDropdownWithDelay();
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (isDropdownOpen && !userMenu.contains(e.target)) {
            hideDropdown();
        }
    });

    // Prevent dropdown from closing when clicking inside it
    userDropdown.addEventListener('click', function(e) {
        e.stopPropagation(); // Prevent event from bubbling up
    });

    // Add keyboard accessibility
    userMenuBtn.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            if (isDropdownOpen) {
                hideDropdown();
            } else {
                showDropdown();
            }
        }
    });

    // Close dropdown with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isDropdownOpen) {
            hideDropdown();
        }
    });

    // Add ARIA attributes for accessibility
    userMenuBtn.setAttribute('aria-haspopup', 'true');
    userMenuBtn.setAttribute('aria-expanded', 'false');
});
